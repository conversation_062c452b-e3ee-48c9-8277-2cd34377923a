"""
Script de téléchargement pour récupérer l'installateur INVESTT
"""
import os
import shutil
from pathlib import Path

def create_installer_on_desktop():
    """Créer l'installateur sur le bureau"""
    
    # Code de l'installateur complet
    installer_code = '''"""
🚀 INVESTT Trading Bot - Installateur Complet
Installation automatique avec interface moderne
"""
import subprocess
import sys
import os
import shutil
from pathlib import Path
import time

def print_header():
    """Afficher l'en-tête stylé"""
    print("\\n" + "="*60)
    print("🚀 INVESTT TRADING BOT - INSTALLATEUR COMPLET")
    print("="*60)
    print("Installation automatique de votre bot de trading IA")
    print("="*60 + "\\n")

def check_python():
    """Vérifier la version de Python"""
    print("🐍 Vérification de Python...")
    
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ requis")
            print("📥 Téléchargez Python depuis: https://python.org")
            return False
        
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} détecté")
        return True
        
    except Exception as e:
        print(f"❌ Erreur vérification Python: {e}")
        return False

def install_dependencies():
    """Installer toutes les dépendances"""
    print("\\n📦 Installation des dépendances...")
    
    # Dépendances essentielles
    essential_deps = [
        "customtkinter>=5.2.0",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "python-dotenv>=1.0.0",
        "loguru>=0.7.0",
        "ccxt>=4.0.0",
        "pandas-ta>=0.3.14b0",
        "websocket-client>=1.6.0",
        "sqlalchemy>=2.0.0",
        "plotly>=5.15.0",
        "pillow>=10.0.0"
    ]
    
    success_count = 0
    
    # Installer les dépendances essentielles
    print("📋 Installation des dépendances essentielles...")
    for i, dep in enumerate(essential_deps, 1):
        try:
            print(f"   [{i}/{len(essential_deps)}] {dep.split('>=')[0]}...", end=" ")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dep],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅")
                success_count += 1
            else:
                print("❌")
                
        except Exception as e:
            print(f"❌ {e}")
    
    print(f"\\n📊 Installation terminée: {success_count}/{len(essential_deps)} paquets installés")
    return success_count > len(essential_deps) * 0.8  # 80% de succès minimum

def setup_directories():
    """Créer les répertoires nécessaires"""
    print("\\n📁 Création des répertoires...")
    
    directories = ["logs", "data", "backups", "assets"]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"   ✅ {directory}/")
        except Exception as e:
            print(f"   ❌ {directory}/ - {e}")

def create_config_file():
    """Créer le fichier de configuration"""
    print("\\n⚙️ Création du fichier de configuration...")
    
    config_content = """# INVESTT Trading Bot Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=true

# Trading Parameters
INITIAL_CAPITAL=1000.0
MAX_POSITION_SIZE=0.02
MAX_DAILY_LOSS=150.0
MAX_DRAWDOWN=0.10

# Strategy Parameters
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0

# Mode - CHANGEZ ICI POUR LIVE TRADING
PAPER_TRADING=true
LIVE_TRADING=false

# Logging
LOG_LEVEL=INFO
"""
    
    try:
        with open(".env", "w") as f:
            f.write(config_content)
        print("   ✅ Fichier .env créé")
    except Exception as e:
        print(f"   ❌ Erreur création .env: {e}")

def show_completion():
    """Afficher les informations de fin"""
    print("\\n" + "="*60)
    print("🎉 INSTALLATION TERMINÉE!")
    print("="*60)
    print("\\n🚀 Prochaines étapes:")
    print("   1. Téléchargez les fichiers du bot INVESTT")
    print("   2. Placez-les dans ce dossier")
    print("   3. Éditez le fichier .env avec vos clés API")
    print("   4. Lancez: python investt_app.py")
    print("\\n💡 Les dépendances sont maintenant installées!")

def main():
    """Installation principale"""
    print_header()
    
    if not check_python():
        input("\\nAppuyez sur Entrée pour fermer...")
        return
    
    if install_dependencies():
        setup_directories()
        create_config_file()
        show_completion()
    else:
        print("\\n❌ Échec de l'installation des dépendances")
    
    input("\\nAppuyez sur Entrée pour fermer...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\\n\\n❌ Installation interrompue")
    except Exception as e:
        print(f"\\n\\n❌ Erreur: {e}")
    finally:
        input("\\nAppuyez sur Entrée pour fermer...")
'''

    # Chemin du bureau
    try:
        import winshell
        desktop = winshell.desktop()
    except:
        # Fallback pour Windows
        desktop = os.path.join(os.path.expanduser("~"), "Desktop")
    
    # Créer le fichier sur le bureau
    installer_path = os.path.join(desktop, "install_investt.py")
    
    try:
        with open(installer_path, 'w', encoding='utf-8') as f:
            f.write(installer_code)
        
        print("✅ Installateur créé sur le bureau!")
        print(f"📁 Emplacement: {installer_path}")
        print("\n🚀 Double-cliquez sur 'install_investt.py' pour installer!")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("\n💡 Copiez le code manuellement depuis la console")

if __name__ == "__main__":
    create_installer_on_desktop()
    input("\nAppuyez sur Entrée pour fermer...")
