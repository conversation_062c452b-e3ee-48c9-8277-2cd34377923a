"""
Quick start script for INVESTT Trading Bot
"""
import os
import sys
import time
import threading
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from config import config
from src.utils.logger import logger
from src.database import init_db
from src.trading import trading_engine
from src.strategies import create_rsi_strategy


def setup_demo_environment():
    """Setup demo environment with safe defaults"""
    print("🔧 Setting up demo environment...")
    
    # Create .env file with demo settings if it doesn't exist
    env_file = Path('.env')
    if not env_file.exists():
        demo_config = """# INVESTT Demo Configuration
BINANCE_API_KEY=demo_key
BINANCE_SECRET_KEY=demo_secret
BINANCE_TESTNET=true

# Trading Parameters (Demo - Safe Values)
INITIAL_CAPITAL=1000.0
MAX_POSITION_SIZE=0.01
MAX_DAILY_LOSS=50.0
MAX_DRAWDOWN=0.05

# Strategy Parameters
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0

# Timeframes
PRIMARY_TIMEFRAME=5m
TRADING_PAIRS=BTC/USDT,ETH/USDT

# Mode
PAPER_TRADING=true
LIVE_TRADING=false

# Logging
LOG_LEVEL=INFO
"""
        with open(env_file, 'w') as f:
            f.write(demo_config)
        print("✅ Created demo .env configuration")
    
    # Create directories
    for directory in ['logs', 'data', 'backups']:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Demo environment ready")


def run_demo():
    """Run a demo of the trading bot"""
    print("\n🚀 Starting INVESTT Trading Bot Demo")
    print("=" * 50)
    
    try:
        # Initialize database
        print("📊 Initializing database...")
        init_db()
        
        # Create demo strategy
        print("🎯 Setting up RSI strategy...")
        rsi_strategy = create_rsi_strategy(
            symbols=['BTC/USDT', 'ETH/USDT'],
            timeframe='5m',
            rsi_period=14,
            oversold=30,
            overbought=70
        )
        
        # Add strategy to trading engine
        trading_engine.add_strategy(rsi_strategy)
        
        print("✅ Strategy configured")
        print(f"   - Symbols: {rsi_strategy.symbols}")
        print(f"   - Timeframe: {rsi_strategy.timeframe}")
        print(f"   - RSI Period: {rsi_strategy.rsi_period}")
        
        # Start trading engine
        print("\n🔄 Starting trading engine...")
        trading_engine.start()
        
        print("✅ Trading engine started")
        print("\n📈 Demo Status:")
        print(f"   - Mode: {'Paper Trading' if config.PAPER_TRADING else 'Live Trading'}")
        print(f"   - Initial Capital: ${config.INITIAL_CAPITAL:,.2f}")
        print(f"   - Max Daily Loss: ${config.MAX_DAILY_LOSS:,.2f}")
        print(f"   - Max Position Size: {config.MAX_POSITION_SIZE:.1%}")
        
        # Run demo for a short time
        print("\n⏱️  Running demo for 2 minutes...")
        print("   (In real usage, the bot would run continuously)")
        
        start_time = time.time()
        while time.time() - start_time < 120:  # Run for 2 minutes
            status = trading_engine.get_status()
            
            if status['total_trades'] > 0:
                print(f"\n📊 Status Update:")
                print(f"   - Trades: {status['total_trades']}")
                print(f"   - P&L: ${status['current_pnl']:.2f}")
                print(f"   - Portfolio: ${status['portfolio_value']:.2f}")
            
            time.sleep(30)  # Update every 30 seconds
        
        print("\n⏹️  Demo completed")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
    finally:
        # Stop trading engine
        trading_engine.stop()
        print("✅ Trading engine stopped")


def show_dashboard_info():
    """Show information about the dashboard"""
    print("\n📊 Dashboard Information")
    print("=" * 30)
    print("To view the trading dashboard:")
    print("1. Open a new terminal/command prompt")
    print("2. Navigate to the INVESTT directory")
    print("3. Run: streamlit run dashboard.py")
    print("4. Open your browser to the displayed URL")
    print("\nThe dashboard shows:")
    print("- Real-time portfolio value")
    print("- Trading performance metrics")
    print("- Active positions")
    print("- Recent trades")
    print("- Strategy status")
    print("- Risk management status")


def show_next_steps():
    """Show next steps for users"""
    print("\n🎯 Next Steps")
    print("=" * 20)
    print("1. 📖 Read the README.md for detailed documentation")
    print("2. ⚙️  Edit .env file with your real API keys (for live trading)")
    print("3. 🧪 Run tests: python -m pytest tests/")
    print("4. 🚀 Start full bot: python main.py")
    print("5. 📊 Launch dashboard: streamlit run dashboard.py")
    print("\n⚠️  Important Safety Tips:")
    print("- Always start with paper trading")
    print("- Use testnet before live trading")
    print("- Start with small amounts")
    print("- Monitor performance regularly")
    print("- Never risk more than you can afford to lose")


def main():
    """Main quick start function"""
    print("🚀 INVESTT Trading Bot - Quick Start")
    print("=" * 40)
    print("This demo will show you how the trading bot works")
    print("in a safe paper trading environment.\n")
    
    # Check if user wants to continue
    response = input("Do you want to run the demo? (y/n): ").lower().strip()
    if response != 'y' and response != 'yes':
        print("Demo cancelled. Run 'python quick_start.py' anytime to try again.")
        return
    
    try:
        # Setup demo environment
        setup_demo_environment()
        
        # Run the demo
        run_demo()
        
        # Show additional information
        show_dashboard_info()
        show_next_steps()
        
        print("\n🎉 Quick start demo completed!")
        print("Thank you for trying INVESTT Trading Bot!")
        
    except Exception as e:
        print(f"\n❌ Quick start failed: {e}")
        print("Please check the installation and try again.")
        sys.exit(1)


if __name__ == "__main__":
    main()
