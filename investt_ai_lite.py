"""
🤖 INVESTT AI Trading Bot - Version Lite (Sans Pandas/NumPy)
Super-Algorithme Hybride optimisé pour PyInstaller
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import threading
import time
import random
import math
from datetime import datetime, timedelta
from pathlib import Path
import sys
import os

# Configuration du thème moderne
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

# Configuration intégrée
class Config:
    def __init__(self):
        self.INITIAL_CAPITAL = 1000.0
        self.MAX_DAILY_LOSS = 150.0
        self.MAX_POSITION_SIZE = 0.02
        self.PAPER_TRADING = True
        self.LIVE_TRADING = False
        self.TRADING_PAIRS = ["BTC/USDT", "ETH/USDT", "BNB/USDT", "ADA/USDT", "SOL/USDT"]
        self.PRIMARY_TIMEFRAME = "5m"
        self.BINANCE_API_KEY = ""
        self.BINANCE_SECRET_KEY = ""
        self.load_from_env()
    
    def load_from_env(self):
        """Charger depuis .env si disponible"""
        try:
            env_file = Path(".env")
            if env_file.exists():
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            key, value = key.strip(), value.strip()
                            
                            if key == "INITIAL_CAPITAL":
                                self.INITIAL_CAPITAL = float(value)
                            elif key == "MAX_DAILY_LOSS":
                                self.MAX_DAILY_LOSS = float(value)
                            elif key == "MAX_POSITION_SIZE":
                                self.MAX_POSITION_SIZE = float(value)
                            elif key == "PAPER_TRADING":
                                self.PAPER_TRADING = value.lower() in ('true', '1', 'yes')
                            elif key == "LIVE_TRADING":
                                self.LIVE_TRADING = value.lower() in ('true', '1', 'yes')
                            elif key == "BINANCE_API_KEY":
                                self.BINANCE_API_KEY = value
                            elif key == "BINANCE_SECRET_KEY":
                                self.BINANCE_SECRET_KEY = value
                            elif key == "TRADING_PAIRS":
                                self.TRADING_PAIRS = [p.strip() for p in value.split(',')]
        except Exception as e:
            print(f"Erreur chargement .env: {e}")

config = Config()

# Simulateur d'IA Lite (sans dépendances lourdes)
class AILiteEngine:
    """Moteur IA simplifié sans pandas/numpy"""
    
    def __init__(self):
        self.market_data = {}
        self.ai_models = {}
        self.learning_data = {}
        self.patterns_detected = []
        self.sentiment_score = 0.5
        self.confidence_level = 0.7
        self.ml_accuracy = 0.65
        
        # Paramètres adaptatifs
        self.rsi_oversold = 30.0
        self.rsi_overbought = 70.0
        self.ml_weight = 0.5
        self.technical_weight = 0.5
        self.sentiment_weight = 0.2
        
        # Historique d'apprentissage
        self.trade_history = []
        self.successful_trades = 0
        self.total_trades = 0
    
    def calculate_rsi(self, prices, period=14):
        """Calculer RSI simplifié"""
        try:
            if len(prices) < period + 1:
                return 50.0
            
            gains = []
            losses = []
            
            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            if len(gains) < period:
                return 50.0
            
            avg_gain = sum(gains[-period:]) / period
            avg_loss = sum(losses[-period:]) / period
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return max(0, min(100, rsi))
            
        except:
            return 50.0
    
    def calculate_macd(self, prices):
        """Calculer MACD simplifié"""
        try:
            if len(prices) < 26:
                return 0.0, 0.0, 0.0
            
            # EMA simplifiée
            def simple_ema(data, period):
                if len(data) < period:
                    return data[-1] if data else 0
                multiplier = 2 / (period + 1)
                ema = data[0]
                for price in data[1:]:
                    ema = (price * multiplier) + (ema * (1 - multiplier))
                return ema
            
            ema_12 = simple_ema(prices[-12:], 12)
            ema_26 = simple_ema(prices[-26:], 26)
            macd = ema_12 - ema_26
            
            # Signal line (EMA 9 du MACD)
            signal = macd * 0.8  # Simplification
            histogram = macd - signal
            
            return macd, signal, histogram
            
        except:
            return 0.0, 0.0, 0.0
    
    def detect_patterns(self, prices):
        """Détecter des patterns simples"""
        try:
            patterns = []
            
            if len(prices) < 5:
                return patterns
            
            # Pattern haussier simple
            if prices[-1] > prices[-2] > prices[-3]:
                patterns.append("Tendance Haussière")
            
            # Pattern baissier simple
            if prices[-1] < prices[-2] < prices[-3]:
                patterns.append("Tendance Baissière")
            
            # Doji simplifié
            if len(prices) >= 2:
                change = abs(prices[-1] - prices[-2]) / prices[-2]
                if change < 0.005:  # Moins de 0.5% de changement
                    patterns.append("Doji")
            
            # Support/Résistance
            recent_max = max(prices[-10:]) if len(prices) >= 10 else max(prices)
            recent_min = min(prices[-10:]) if len(prices) >= 10 else min(prices)
            current = prices[-1]
            
            if abs(current - recent_max) / recent_max < 0.01:
                patterns.append("Près Résistance")
            elif abs(current - recent_min) / recent_min < 0.01:
                patterns.append("Près Support")
            
            return patterns
            
        except:
            return []
    
    def analyze_sentiment(self, symbol):
        """Analyser le sentiment simplifié"""
        try:
            # Simulation basée sur des patterns réalistes
            base_sentiment = 0.5
            
            # Facteur temporel (simulation de cycles de marché)
            time_factor = math.sin(time.time() / 3600) * 0.2  # Cycle horaire
            
            # Facteur aléatoire (simulation de news/événements)
            random_factor = (random.random() - 0.5) * 0.3
            
            # Facteur de volatilité
            volatility_factor = random.uniform(-0.1, 0.1)
            
            sentiment = base_sentiment + time_factor + random_factor + volatility_factor
            sentiment = max(0, min(1, sentiment))
            
            # Classification
            if sentiment < 0.2:
                classification = "Très Baissier"
            elif sentiment < 0.4:
                classification = "Baissier"
            elif sentiment < 0.6:
                classification = "Neutre"
            elif sentiment < 0.8:
                classification = "Haussier"
            else:
                classification = "Très Haussier"
            
            return sentiment, classification
            
        except:
            return 0.5, "Neutre"
    
    def ml_prediction(self, symbol, prices):
        """Prédiction ML simplifiée"""
        try:
            if len(prices) < 10:
                return 0.0, 0.5
            
            # Simulation d'un modèle ML basé sur des features simples
            
            # Feature 1: Tendance récente
            recent_trend = (prices[-1] - prices[-5]) / prices[-5] if len(prices) >= 5 else 0
            
            # Feature 2: Volatilité
            price_changes = [abs(prices[i] - prices[i-1])/prices[i-1] for i in range(1, len(prices))]
            volatility = sum(price_changes[-10:]) / min(10, len(price_changes)) if price_changes else 0
            
            # Feature 3: RSI
            rsi = self.calculate_rsi(prices)
            rsi_normalized = (rsi - 50) / 50  # Normaliser entre -1 et 1
            
            # Feature 4: MACD
            macd, signal, _ = self.calculate_macd(prices)
            macd_signal = 1 if macd > signal else -1
            
            # Modèle simplifié (simulation d'un Random Forest)
            prediction = (
                recent_trend * 0.3 +
                rsi_normalized * 0.2 +
                macd_signal * 0.2 +
                (0.5 - volatility) * 0.1 +  # Moins de volatilité = plus de confiance
                random.uniform(-0.1, 0.1)  # Facteur aléatoire
            )
            
            # Confiance basée sur la cohérence des signaux
            confidence = min(0.9, 0.5 + abs(prediction) * 0.5)
            
            return prediction, confidence
            
        except:
            return 0.0, 0.5
    
    def adaptive_decision(self, symbol):
        """Décision adaptative combinant tous les signaux"""
        try:
            # Générer des prix simulés pour l'analyse
            base_price = random.uniform(30000, 70000)  # Prix BTC simulé
            prices = []
            for i in range(50):
                change = random.uniform(-0.02, 0.02)  # ±2% par période
                if i == 0:
                    prices.append(base_price)
                else:
                    new_price = prices[-1] * (1 + change)
                    prices.append(new_price)
            
            # Analyse technique
            rsi = self.calculate_rsi(prices)
            macd, macd_signal, _ = self.calculate_macd(prices)
            
            # Signaux techniques
            tech_signal = 0.0
            if rsi < self.rsi_oversold:
                tech_signal += 0.3  # Signal d'achat
            elif rsi > self.rsi_overbought:
                tech_signal -= 0.3  # Signal de vente
            
            if macd > macd_signal:
                tech_signal += 0.2
            else:
                tech_signal -= 0.2
            
            # Prédiction ML
            ml_pred, ml_conf = self.ml_prediction(symbol, prices)
            
            # Sentiment
            sentiment, sentiment_class = self.analyze_sentiment(symbol)
            sentiment_signal = (sentiment - 0.5) * 2  # Normaliser entre -1 et 1
            
            # Patterns
            patterns = self.detect_patterns(prices)
            pattern_signal = len([p for p in patterns if "Haussière" in p or "Support" in p]) * 0.1
            pattern_signal -= len([p for p in patterns if "Baissière" in p or "Résistance" in p]) * 0.1
            
            # Combinaison adaptative
            final_signal = (
                tech_signal * self.technical_weight +
                ml_pred * self.ml_weight +
                sentiment_signal * self.sentiment_weight +
                pattern_signal * 0.1
            )
            
            # Confiance globale
            confidence = (ml_conf + self.confidence_level) / 2
            
            # Mettre à jour les métriques
            self.sentiment_score = sentiment
            self.confidence_level = confidence
            self.patterns_detected = patterns
            
            # Décision finale
            if abs(final_signal) > 0.3 and confidence > 0.6:
                action = "BUY" if final_signal > 0 else "SELL"
                strength = min(abs(final_signal), 1.0)
                
                return {
                    'action': action,
                    'strength': strength,
                    'confidence': confidence,
                    'price': prices[-1],
                    'signals': {
                        'technical': tech_signal,
                        'ml_prediction': ml_pred,
                        'sentiment': sentiment_signal,
                        'patterns': pattern_signal
                    },
                    'analysis': {
                        'rsi': rsi,
                        'macd': macd,
                        'sentiment_class': sentiment_class,
                        'patterns': patterns
                    }
                }
            
            return None
            
        except Exception as e:
            print(f"Erreur décision adaptative: {e}")
            return None
    
    def learn_from_trade(self, trade_result):
        """Apprendre du résultat d'un trade"""
        try:
            self.total_trades += 1
            
            if trade_result.get('profitable', False):
                self.successful_trades += 1
            
            # Adapter les paramètres selon les performances
            win_rate = self.successful_trades / self.total_trades if self.total_trades > 0 else 0.5
            
            # Si performance faible, ajuster les seuils
            if win_rate < 0.4 and self.total_trades > 10:
                self.rsi_oversold = max(25, self.rsi_oversold - 1)
                self.rsi_overbought = min(75, self.rsi_overbought + 1)
                self.confidence_level = min(0.9, self.confidence_level + 0.05)
            
            # Si bonne performance, être plus agressif
            elif win_rate > 0.7 and self.total_trades > 10:
                self.rsi_oversold = min(35, self.rsi_oversold + 1)
                self.rsi_overbought = max(65, self.rsi_overbought - 1)
                self.confidence_level = max(0.5, self.confidence_level - 0.02)
            
            # Adapter les poids selon l'efficacité
            if trade_result.get('ml_contributed', False) and trade_result.get('profitable', False):
                self.ml_weight = min(0.8, self.ml_weight + 0.01)
                self.technical_weight = max(0.2, 1.0 - self.ml_weight - self.sentiment_weight)
            
            # Mettre à jour la précision ML
            if self.total_trades > 0:
                self.ml_accuracy = self.successful_trades / self.total_trades
            
            # Enregistrer l'historique
            self.trade_history.append({
                'timestamp': datetime.now(),
                'result': trade_result,
                'parameters': {
                    'rsi_oversold': self.rsi_oversold,
                    'rsi_overbought': self.rsi_overbought,
                    'ml_weight': self.ml_weight,
                    'technical_weight': self.technical_weight,
                    'confidence_level': self.confidence_level
                }
            })
            
            # Garder seulement les 1000 derniers
            if len(self.trade_history) > 1000:
                self.trade_history = self.trade_history[-1000:]
                
        except Exception as e:
            print(f"Erreur apprentissage: {e}")
    
    def get_ai_status(self):
        """Obtenir le statut de l'IA"""
        return {
            'confidence': self.confidence_level,
            'ml_accuracy': self.ml_accuracy,
            'sentiment_score': self.sentiment_score,
            'patterns_count': len(self.patterns_detected),
            'total_trades': self.total_trades,
            'win_rate': self.successful_trades / self.total_trades if self.total_trades > 0 else 0,
            'parameters': {
                'rsi_oversold': self.rsi_oversold,
                'rsi_overbought': self.rsi_overbought,
                'ml_weight': self.ml_weight,
                'technical_weight': self.technical_weight,
                'sentiment_weight': self.sentiment_weight
            }
        }

# Instance globale de l'IA
ai_engine = AILiteEngine()


class AITradingAppLite:
    """Application de Trading IA Lite"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.start_update_loops()
        
        # État du bot
        self.bot_running = False
        self.bot_paused = False
        self.trades_count = 0
        self.successful_trades = 0
        self.portfolio_value_num = config.INITIAL_CAPITAL
        self.daily_pnl_num = 0.0
    
    def setup_window(self):
        """Configuration de la fenêtre"""
        self.root.title("🤖 INVESTT AI Trading Bot - Super-Algorithme Lite")
        self.root.geometry("1600x1000")
        self.root.minsize(1400, 900)
        
        # Centrer la fenêtre
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (1000 // 2)
        self.root.geometry(f"1600x1000+{x}+{y}")
    
    def setup_variables(self):
        """Initialisation des variables"""
        self.is_live_trading = tk.BooleanVar(value=config.LIVE_TRADING)
        
        # Métriques principales
        self.portfolio_value = tk.StringVar(value=f"${config.INITIAL_CAPITAL:,.2f}")
        self.daily_pnl = tk.StringVar(value="$0.00")
        self.total_trades = tk.StringVar(value="0")
        self.win_rate = tk.StringVar(value="0%")
        self.status_text = tk.StringVar(value="🔴 IA Arrêtée")
        
        # Métriques IA
        self.ai_confidence = tk.StringVar(value="70%")
        self.ml_accuracy = tk.StringVar(value="65%")
        self.market_sentiment = tk.StringVar(value="Neutre")
        self.patterns_detected = tk.StringVar(value="0")
        
        # Configuration
        self.capital = tk.StringVar(value=str(config.INITIAL_CAPITAL))
        self.max_loss = tk.StringVar(value=str(config.MAX_DAILY_LOSS))
        self.position_size = tk.StringVar(value=str(config.MAX_POSITION_SIZE * 100))
        
        # Paramètres IA
        self.ai_enabled = tk.BooleanVar(value=True)
        self.ml_weight = tk.StringVar(value="50")
        self.technical_weight = tk.StringVar(value="50")
        self.sentiment_weight = tk.StringVar(value="20")

    def create_widgets(self):
        """Création de l'interface utilisateur"""

        # === HEADER ===
        header_frame = ctk.CTkFrame(self.root, height=80, corner_radius=0)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)

        # Titre avec IA
        title_label = ctk.CTkLabel(
            header_frame,
            text="🤖 INVESTT AI TRADING BOT - SUPER-ALGORITHME LITE",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=20)

        # Status IA
        ai_status_frame = ctk.CTkFrame(header_frame)
        ai_status_frame.pack(side="right", padx=20, pady=15)

        ctk.CTkLabel(ai_status_frame, text="🧠 IA:", font=ctk.CTkFont(size=12)).pack(side="left", padx=5)
        ctk.CTkLabel(
            ai_status_frame,
            text="LITE ACTIVE",
            text_color="green",
            font=ctk.CTkFont(size=12, weight="bold")
        ).pack(side="left", padx=5)

        # Status bot
        self.status_label = ctk.CTkLabel(
            header_frame,
            textvariable=self.status_text,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.status_label.pack(side="right", padx=(0, 20), pady=20)

        # === MAIN CONTAINER ===
        main_container = ctk.CTkFrame(self.root, corner_radius=0)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)

        # === LEFT PANEL - CONTRÔLES ===
        left_panel = ctk.CTkFrame(main_container, width=400)
        left_panel.pack(side="left", fill="y", padx=(0, 10))
        left_panel.pack_propagate(False)

        self.create_control_panel(left_panel)

        # === CENTER PANEL - MONITORING ===
        center_panel = ctk.CTkFrame(main_container, width=600)
        center_panel.pack(side="left", fill="both", expand=True, padx=(0, 10))

        self.create_monitoring_panel(center_panel)

        # === RIGHT PANEL - IA ANALYTICS ===
        right_panel = ctk.CTkFrame(main_container, width=400)
        right_panel.pack(side="right", fill="y")
        right_panel.pack_propagate(False)

        self.create_ai_panel(right_panel)

    def create_control_panel(self, parent):
        """Panneau de contrôle avec paramètres IA"""

        # Titre
        ctk.CTkLabel(
            parent,
            text="⚙️ CONTRÔLES IA LITE",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(15, 10))

        # === MODE DE TRADING ===
        mode_frame = ctk.CTkFrame(parent)
        mode_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            mode_frame,
            text="💰 Mode de Trading",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        mode_switch = ctk.CTkSwitch(
            mode_frame,
            text="LIVE TRADING",
            variable=self.is_live_trading,
            command=self.toggle_trading_mode,
            font=ctk.CTkFont(size=12, weight="bold")
        )
        mode_switch.pack(pady=5)

        self.warning_label = ctk.CTkLabel(
            mode_frame,
            text="⚠️ ARGENT RÉEL EN JEU !",
            text_color="red",
            font=ctk.CTkFont(size=10, weight="bold")
        )

        # === CONTRÔLES IA ===
        ai_controls_frame = ctk.CTkFrame(parent)
        ai_controls_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            ai_controls_frame,
            text="🤖 Paramètres IA Lite",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        # Switch IA
        ai_switch = ctk.CTkSwitch(
            ai_controls_frame,
            text="IA Activée",
            variable=self.ai_enabled,
            font=ctk.CTkFont(size=12)
        )
        ai_switch.pack(pady=5)

        # Poids ML
        ctk.CTkLabel(ai_controls_frame, text="Poids ML (%)", font=ctk.CTkFont(size=11)).pack(anchor="w", padx=10, pady=(5,0))
        ml_slider = ctk.CTkSlider(ai_controls_frame, from_=0, to=100, variable=self.ml_weight, command=self.update_ai_weights)
        ml_slider.pack(fill="x", padx=10, pady=2)

        # Poids Technique
        ctk.CTkLabel(ai_controls_frame, text="Poids Technique (%)", font=ctk.CTkFont(size=11)).pack(anchor="w", padx=10, pady=(5,0))
        tech_slider = ctk.CTkSlider(ai_controls_frame, from_=0, to=100, variable=self.technical_weight, command=self.update_ai_weights)
        tech_slider.pack(fill="x", padx=10, pady=2)

        # Poids Sentiment
        ctk.CTkLabel(ai_controls_frame, text="Poids Sentiment (%)", font=ctk.CTkFont(size=11)).pack(anchor="w", padx=10, pady=(5,0))
        sentiment_slider = ctk.CTkSlider(ai_controls_frame, from_=0, to=50, variable=self.sentiment_weight, command=self.update_ai_weights)
        sentiment_slider.pack(fill="x", padx=10, pady=(2,10))

        # === BOUTONS PRINCIPAUX ===
        buttons_frame = ctk.CTkFrame(parent)
        buttons_frame.pack(fill="x", padx=15, pady=15)

        self.start_button = ctk.CTkButton(
            buttons_frame,
            text="🚀 DÉMARRER IA LITE",
            command=self.toggle_bot,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=45,
            fg_color="green",
            hover_color="darkgreen"
        )
        self.start_button.pack(fill="x", pady=3)

        self.pause_button = ctk.CTkButton(
            buttons_frame,
            text="⏸️ PAUSE",
            command=self.pause_bot,
            font=ctk.CTkFont(size=12),
            height=35,
            state="disabled"
        )
        self.pause_button.pack(fill="x", pady=3)

        emergency_button = ctk.CTkButton(
            buttons_frame,
            text="🛑 ARRÊT D'URGENCE",
            command=self.emergency_stop,
            font=ctk.CTkFont(size=12, weight="bold"),
            height=35,
            fg_color="red",
            hover_color="darkred"
        )
        emergency_button.pack(fill="x", pady=3)

        # === CONFIGURATION ===
        config_frame = ctk.CTkFrame(parent)
        config_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            config_frame,
            text="⚙️ Configuration",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        # Capital
        ctk.CTkLabel(config_frame, text="Capital ($)", font=ctk.CTkFont(size=11)).pack(anchor="w", padx=10)
        capital_entry = ctk.CTkEntry(config_frame, textvariable=self.capital, height=30)
        capital_entry.pack(fill="x", padx=10, pady=2)

        # Perte max
        ctk.CTkLabel(config_frame, text="Perte Max ($)", font=ctk.CTkFont(size=11)).pack(anchor="w", padx=10)
        loss_entry = ctk.CTkEntry(config_frame, textvariable=self.max_loss, height=30)
        loss_entry.pack(fill="x", padx=10, pady=2)

        # Position size
        ctk.CTkLabel(config_frame, text="Position Max (%)", font=ctk.CTkFont(size=11)).pack(anchor="w", padx=10)
        position_entry = ctk.CTkEntry(config_frame, textvariable=self.position_size, height=30)
        position_entry.pack(fill="x", padx=10, pady=(2, 10))

        save_btn = ctk.CTkButton(
            config_frame,
            text="💾 Sauvegarder",
            command=self.save_config,
            height=30
        )
        save_btn.pack(fill="x", padx=10, pady=(0, 10))

        # === CLÉS API ===
        api_frame = ctk.CTkFrame(parent)
        api_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            api_frame,
            text="🔑 Clés API",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        self.api_key_entry = ctk.CTkEntry(api_frame, placeholder_text="API Key", height=30)
        self.api_key_entry.pack(fill="x", padx=10, pady=2)

        self.secret_key_entry = ctk.CTkEntry(api_frame, placeholder_text="Secret Key", show="*", height=30)
        self.secret_key_entry.pack(fill="x", padx=10, pady=(2, 10))

        test_btn = ctk.CTkButton(
            api_frame,
            text="🔍 Tester",
            command=self.test_api_connection,
            height=30
        )
        test_btn.pack(fill="x", padx=10, pady=(0, 10))

    def create_monitoring_panel(self, parent):
        """Panneau de monitoring principal"""

        # Titre
        ctk.CTkLabel(
            parent,
            text="📊 MONITORING IA TEMPS RÉEL",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(15, 10))

        # === MÉTRIQUES PRINCIPALES ===
        metrics_frame = ctk.CTkFrame(parent)
        metrics_frame.pack(fill="x", padx=15, pady=10)

        # Grille 2x2
        metrics_grid = ctk.CTkFrame(metrics_frame)
        metrics_grid.pack(fill="x", padx=10, pady=10)

        # Portfolio
        portfolio_frame = ctk.CTkFrame(metrics_grid)
        portfolio_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(portfolio_frame, text="💼 Portfolio", font=ctk.CTkFont(size=11)).pack()
        self.portfolio_label = ctk.CTkLabel(
            portfolio_frame,
            textvariable=self.portfolio_value,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="lightblue"
        )
        self.portfolio_label.pack()

        # P&L
        pnl_frame = ctk.CTkFrame(metrics_grid)
        pnl_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(pnl_frame, text="📈 P&L Quotidien", font=ctk.CTkFont(size=11)).pack()
        self.pnl_label = ctk.CTkLabel(
            pnl_frame,
            textvariable=self.daily_pnl,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.pnl_label.pack()

        # Trades
        trades_frame = ctk.CTkFrame(metrics_grid)
        trades_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(trades_frame, text="🔄 Trades IA", font=ctk.CTkFont(size=11)).pack()
        ctk.CTkLabel(
            trades_frame,
            textvariable=self.total_trades,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="orange"
        ).pack()

        # Win Rate
        winrate_frame = ctk.CTkFrame(metrics_grid)
        winrate_frame.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(winrate_frame, text="🎯 Win Rate IA", font=ctk.CTkFont(size=11)).pack()
        ctk.CTkLabel(
            winrate_frame,
            textvariable=self.win_rate,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="lightgreen"
        ).pack()

        metrics_grid.grid_columnconfigure(0, weight=1)
        metrics_grid.grid_columnconfigure(1, weight=1)

        # === MÉTRIQUES IA LITE ===
        ai_metrics_frame = ctk.CTkFrame(parent)
        ai_metrics_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            ai_metrics_frame,
            text="🤖 Métriques IA Lite",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        ai_grid = ctk.CTkFrame(ai_metrics_frame)
        ai_grid.pack(fill="x", padx=10, pady=5)

        # Confiance IA
        conf_frame = ctk.CTkFrame(ai_grid)
        conf_frame.grid(row=0, column=0, padx=3, pady=3, sticky="ew")
        ctk.CTkLabel(conf_frame, text="🧠 Confiance", font=ctk.CTkFont(size=10)).pack()
        ctk.CTkLabel(
            conf_frame,
            textvariable=self.ai_confidence,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="cyan"
        ).pack()

        # Précision ML
        ml_frame = ctk.CTkFrame(ai_grid)
        ml_frame.grid(row=0, column=1, padx=3, pady=3, sticky="ew")
        ctk.CTkLabel(ml_frame, text="🎯 ML Accuracy", font=ctk.CTkFont(size=10)).pack()
        ctk.CTkLabel(
            ml_frame,
            textvariable=self.ml_accuracy,
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color="yellow"
        ).pack()

        ai_grid.grid_columnconfigure(0, weight=1)
        ai_grid.grid_columnconfigure(1, weight=1)

        # === LOGS ===
        log_frame = ctk.CTkFrame(parent)
        log_frame.pack(fill="both", expand=True, padx=15, pady=10)

        ctk.CTkLabel(
            log_frame,
            text="📝 Logs IA Lite en Temps Réel",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        self.log_text = ctk.CTkTextbox(
            log_frame,
            font=ctk.CTkFont(family="Consolas", size=10),
            wrap="word"
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))

        clear_btn = ctk.CTkButton(
            log_frame,
            text="🗑️ Effacer",
            command=self.clear_logs,
            height=25
        )
        clear_btn.pack(pady=(0, 10))

    def create_ai_panel(self, parent):
        """Panneau d'analytics IA Lite"""

        # Titre
        ctk.CTkLabel(
            parent,
            text="🧠 IA LITE ANALYTICS",
            font=ctk.CTkFont(size=18, weight="bold")
        ).pack(pady=(15, 10))

        # === SENTIMENT DE MARCHÉ ===
        sentiment_frame = ctk.CTkFrame(parent)
        sentiment_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            sentiment_frame,
            text="📰 Sentiment de Marché",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        self.sentiment_label = ctk.CTkLabel(
            sentiment_frame,
            textvariable=self.market_sentiment,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="lightgreen"
        )
        self.sentiment_label.pack(pady=5)

        # Barre de sentiment
        self.sentiment_progress = ctk.CTkProgressBar(sentiment_frame)
        self.sentiment_progress.pack(fill="x", padx=10, pady=(0, 10))
        self.sentiment_progress.set(0.5)

        # === PATTERNS DÉTECTÉS ===
        patterns_frame = ctk.CTkFrame(parent)
        patterns_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            patterns_frame,
            text="🔍 Patterns IA Détectés",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        ctk.CTkLabel(
            patterns_frame,
            textvariable=self.patterns_detected,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color="orange"
        ).pack(pady=5)

        # Liste des patterns
        self.patterns_list = ctk.CTkTextbox(
            patterns_frame,
            height=100,
            font=ctk.CTkFont(size=10)
        )
        self.patterns_list.pack(fill="x", padx=10, pady=(0, 10))

        # === PRÉDICTIONS ML LITE ===
        predictions_frame = ctk.CTkFrame(parent)
        predictions_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            predictions_frame,
            text="🔮 Prédictions ML Lite",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        # Tableau des prédictions
        self.predictions_text = ctk.CTkTextbox(
            predictions_frame,
            height=120,
            font=ctk.CTkFont(size=10)
        )
        self.predictions_text.pack(fill="x", padx=10, pady=(0, 10))

        # === PARAMÈTRES ADAPTATIFS ===
        strategy_frame = ctk.CTkFrame(parent)
        strategy_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            strategy_frame,
            text="🎯 Paramètres Adaptatifs",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        # Paramètres actuels
        self.strategy_params = ctk.CTkTextbox(
            strategy_frame,
            height=100,
            font=ctk.CTkFont(size=9)
        )
        self.strategy_params.pack(fill="x", padx=10, pady=(0, 10))

        # === ACTIONS IA LITE ===
        ai_actions_frame = ctk.CTkFrame(parent)
        ai_actions_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            ai_actions_frame,
            text="🔧 Actions IA Lite",
            font=ctk.CTkFont(size=14, weight="bold")
        ).pack(pady=(10, 5))

        retrain_btn = ctk.CTkButton(
            ai_actions_frame,
            text="🔄 Optimiser IA",
            command=self.optimize_ai,
            height=30
        )
        retrain_btn.pack(fill="x", padx=10, pady=2)

        analyze_btn = ctk.CTkButton(
            ai_actions_frame,
            text="📊 Analyser Marché",
            command=self.analyze_market,
            height=30
        )
        analyze_btn.pack(fill="x", padx=10, pady=2)

        reset_btn = ctk.CTkButton(
            ai_actions_frame,
            text="🔄 Reset IA",
            command=self.reset_ai,
            height=30
        )
        reset_btn.pack(fill="x", padx=10, pady=(2, 10))

    def update_ai_weights(self, value=None):
        """Mettre à jour les poids de l'IA"""
        try:
            ai_engine.ml_weight = float(self.ml_weight.get()) / 100
            ai_engine.technical_weight = float(self.technical_weight.get()) / 100
            ai_engine.sentiment_weight = float(self.sentiment_weight.get()) / 100

            self.log_message(f"🎯 Poids IA mis à jour: ML={ai_engine.ml_weight:.2f}, Tech={ai_engine.technical_weight:.2f}, Sentiment={ai_engine.sentiment_weight:.2f}")
        except:
            pass

    def toggle_trading_mode(self):
        """Basculer entre live et paper trading"""
        if self.is_live_trading.get():
            result = messagebox.askyesno(
                "⚠️ CONFIRMATION LIVE TRADING IA",
                "ATTENTION: Vous allez activer le LIVE TRADING avec l'IA!\n\n"
                "L'IA Lite utilisera de l'ARGENT RÉEL pour trader.\n"
                "Êtes-vous sûr de vouloir continuer?",
                icon="warning"
            )

            if result:
                config.LIVE_TRADING = True
                config.PAPER_TRADING = False
                self.warning_label.pack(pady=5)
                self.log_message("⚠️ LIVE TRADING IA ACTIVÉ - ARGENT RÉEL!")
            else:
                self.is_live_trading.set(False)
        else:
            config.LIVE_TRADING = False
            config.PAPER_TRADING = True
            self.warning_label.pack_forget()
            self.log_message("📝 Paper Trading IA activé - Mode sécurisé")

    def toggle_bot(self):
        """Démarrer/Arrêter le bot IA"""
        if not self.bot_running:
            self.start_ai_bot()
        else:
            self.stop_ai_bot()

    def start_ai_bot(self):
        """Démarrer le bot de trading IA Lite"""
        try:
            # Validation
            if self.is_live_trading.get():
                api_key = self.api_key_entry.get().strip()
                secret_key = self.secret_key_entry.get().strip()

                if not api_key or not secret_key:
                    messagebox.showerror(
                        "Erreur Configuration",
                        "Clés API manquantes pour le live trading IA!"
                    )
                    return

                config.BINANCE_API_KEY = api_key
                config.BINANCE_SECRET_KEY = secret_key

            self.bot_running = True
            self.start_button.configure(
                text="🛑 ARRÊTER IA",
                fg_color="red",
                hover_color="darkred"
            )
            self.pause_button.configure(state="normal")
            self.status_text.set("🤖 IA LITE EN FONCTIONNEMENT")

            mode = "LIVE" if self.is_live_trading.get() else "PAPER"
            self.log_message(f"🚀 Bot IA Lite démarré en mode {mode}")

            self.log_message("🧠 Modules IA Lite activés:")
            self.log_message("  • Analyse technique simplifiée")
            self.log_message("  • Prédictions ML légères")
            self.log_message("  • Détection de patterns basique")
            self.log_message("  • Analyse de sentiment simulée")
            self.log_message("  • Apprentissage adaptatif")

            # Démarrer la simulation IA
            self.start_ai_simulation()

        except Exception as e:
            self.log_message(f"❌ Erreur démarrage IA: {e}")
            messagebox.showerror("Erreur", f"Impossible de démarrer l'IA:\n{e}")

    def stop_ai_bot(self):
        """Arrêter le bot IA"""
        self.bot_running = False
        self.bot_paused = False

        self.start_button.configure(
            text="🚀 DÉMARRER IA LITE",
            fg_color="green",
            hover_color="darkgreen"
        )
        self.pause_button.configure(state="disabled", text="⏸️ PAUSE")
        self.status_text.set("🔴 IA LITE ARRÊTÉE")

        self.log_message("🛑 Bot IA Lite arrêté")

    def pause_bot(self):
        """Mettre en pause/reprendre l'IA"""
        if self.bot_paused:
            self.bot_paused = False
            self.pause_button.configure(text="⏸️ PAUSE")
            self.status_text.set("🤖 IA LITE EN FONCTIONNEMENT")
            self.log_message("▶️ IA Lite reprise")
        else:
            self.bot_paused = True
            self.pause_button.configure(text="▶️ REPRENDRE")
            self.status_text.set("⏸️ IA LITE EN PAUSE")
            self.log_message("⏸️ IA Lite en pause")

    def emergency_stop(self):
        """Arrêt d'urgence de l'IA"""
        result = messagebox.askyesno(
            "🛑 ARRÊT D'URGENCE IA LITE",
            "Voulez-vous vraiment effectuer un arrêt d'urgence?\n\n"
            "Cela arrêtera immédiatement l'IA Lite\n"
            "et fermera toutes les positions.",
            icon="warning"
        )

        if result:
            self.stop_ai_bot()
            self.log_message("🛑 ARRÊT D'URGENCE IA LITE EFFECTUÉ!")
            messagebox.showinfo("Arrêt d'Urgence", "IA Lite arrêtée avec succès!")

    def save_config(self):
        """Sauvegarder la configuration"""
        try:
            config.INITIAL_CAPITAL = float(self.capital.get())
            config.MAX_DAILY_LOSS = float(self.max_loss.get())
            config.MAX_POSITION_SIZE = float(self.position_size.get()) / 100

            self.save_to_env()
            self.log_message("💾 Configuration IA sauvegardée")
            messagebox.showinfo("Configuration", "Configuration IA sauvegardée!")

        except ValueError:
            messagebox.showerror("Erreur", "Valeurs de configuration invalides!")
        except Exception as e:
            self.log_message(f"❌ Erreur sauvegarde: {e}")

    def save_to_env(self):
        """Sauvegarder dans .env"""
        try:
            env_content = f"""# INVESTT AI Lite Trading Bot Configuration
BINANCE_API_KEY={config.BINANCE_API_KEY}
BINANCE_SECRET_KEY={config.BINANCE_SECRET_KEY}
BINANCE_TESTNET=true

# Trading Parameters
INITIAL_CAPITAL={config.INITIAL_CAPITAL}
MAX_POSITION_SIZE={config.MAX_POSITION_SIZE}
MAX_DAILY_LOSS={config.MAX_DAILY_LOSS}

# AI Lite Parameters
AI_ENABLED=true
ML_WEIGHT={self.ml_weight.get()}
TECHNICAL_WEIGHT={self.technical_weight.get()}
SENTIMENT_WEIGHT={self.sentiment_weight.get()}

# Mode
PAPER_TRADING={str(config.PAPER_TRADING).lower()}
LIVE_TRADING={str(config.LIVE_TRADING).lower()}
"""

            with open(".env", "w") as f:
                f.write(env_content)

        except Exception as e:
            print(f"Erreur sauvegarde .env: {e}")

    def test_api_connection(self):
        """Tester la connexion API"""
        api_key = self.api_key_entry.get().strip()
        secret_key = self.secret_key_entry.get().strip()

        if not api_key or not secret_key:
            messagebox.showerror("Erreur", "Veuillez saisir vos clés API!")
            return

        self.log_message("🔍 Test de connexion API...")

        # Simuler un test
        def test_connection():
            time.sleep(2)
            self.root.after(0, lambda: self._finish_api_test(True))

        threading.Thread(target=test_connection, daemon=True).start()

    def _finish_api_test(self, success):
        """Terminer le test API"""
        if success:
            self.log_message("✅ Connexion API réussie!")
            messagebox.showinfo("Test API", "Connexion API réussie!")
        else:
            self.log_message("❌ Échec connexion API")
            messagebox.showerror("Test API", "Échec de la connexion API!")

    def start_ai_simulation(self):
        """Démarrer la simulation IA Lite"""
        def ai_simulation():
            while self.bot_running:
                if not self.bot_paused:
                    # Simulation d'analyse IA Lite
                    self._simulate_ai_analysis()

                    # Simulation de trading IA
                    if random.random() < 0.25:  # 25% de chance par cycle
                        self._simulate_ai_trade()

                time.sleep(3)  # Cycle toutes les 3 secondes

        threading.Thread(target=ai_simulation, daemon=True).start()

    def _simulate_ai_analysis(self):
        """Simuler une analyse IA Lite complète"""
        try:
            # Obtenir le statut de l'IA
            ai_status = ai_engine.get_ai_status()

            # Mettre à jour les métriques IA
            self.root.after(0, lambda: self.ai_confidence.set(f"{ai_status['confidence']*100:.1f}%"))
            self.root.after(0, lambda: self.ml_accuracy.set(f"{ai_status['ml_accuracy']*100:.1f}%"))

            # Simuler l'analyse de sentiment
            sentiment_score, sentiment_class = ai_engine.analyze_sentiment("BTC/USDT")
            self.root.after(0, lambda: self.market_sentiment.set(sentiment_class))
            self.root.after(0, lambda: self.sentiment_progress.set(sentiment_score))

            # Mettre à jour la couleur du sentiment
            if sentiment_score < 0.3:
                color = "red"
            elif sentiment_score < 0.7:
                color = "orange"
            else:
                color = "lightgreen"
            self.root.after(0, lambda: self.sentiment_label.configure(text_color=color))

            # Simuler les patterns détectés
            patterns = ai_engine.patterns_detected
            patterns_count = len(patterns)
            self.root.after(0, lambda: self.patterns_detected.set(str(patterns_count)))

            if patterns:
                patterns_text = '\n'.join([f"• {p}" for p in patterns])
                self.root.after(0, lambda: self._update_patterns_list(patterns_text))

            # Simuler les prédictions ML
            predictions = []
            for symbol in config.TRADING_PAIRS[:3]:
                prediction, confidence = ai_engine.ml_prediction(symbol, [random.uniform(30000, 70000) for _ in range(20)])
                direction = "↗️ Hausse" if prediction > 0.01 else "↘️ Baisse" if prediction < -0.01 else "➡️ Stable"
                predictions.append(f"{symbol}: {direction} ({confidence*100:.1f}%)")

            pred_text = '\n'.join(predictions)
            self.root.after(0, lambda: self._update_predictions(pred_text))

            # Mettre à jour les paramètres adaptatifs
            params_text = f"""RSI Oversold: {ai_status['parameters']['rsi_oversold']:.1f}
RSI Overbought: {ai_status['parameters']['rsi_overbought']:.1f}
ML Weight: {ai_status['parameters']['ml_weight']:.2f}
Tech Weight: {ai_status['parameters']['technical_weight']:.2f}
Confiance: {ai_status['confidence']:.2f}
Trades: {ai_status['total_trades']}
Win Rate: {ai_status['win_rate']*100:.1f}%"""

            self.root.after(0, lambda: self._update_strategy_params(params_text))

        except Exception as e:
            print(f"Erreur simulation IA Lite: {e}")

    def _simulate_ai_trade(self):
        """Simuler un trade IA Lite"""
        try:
            symbol = random.choice(config.TRADING_PAIRS)

            # Obtenir une décision de l'IA
            decision = ai_engine.adaptive_decision(symbol)

            if decision:
                self.trades_count += 1

                # Simuler le résultat du trade basé sur la force du signal
                success_probability = decision['confidence'] * decision['strength']
                is_success = random.random() < success_probability

                if is_success:
                    self.successful_trades += 1
                    trade_pnl = random.uniform(5, 25) * decision['strength']
                else:
                    trade_pnl = -random.uniform(3, 15) * decision['strength']

                self.daily_pnl_num += trade_pnl
                self.portfolio_value_num += trade_pnl

                # Apprentissage de l'IA
                trade_result = {
                    'profitable': is_success,
                    'pnl': trade_pnl,
                    'ml_contributed': decision['signals']['ml_prediction'] != 0,
                    'confidence': decision['confidence']
                }
                ai_engine.learn_from_trade(trade_result)

                # Mettre à jour l'affichage
                self.root.after(0, lambda: self._update_display_after_ai_trade(trade_pnl, is_success, decision, symbol))

        except Exception as e:
            print(f"Erreur simulation trade IA Lite: {e}")

    def _update_display_after_ai_trade(self, trade_pnl, is_success, decision, symbol):
        """Mettre à jour l'affichage après un trade IA"""
        try:
            self.total_trades.set(str(self.trades_count))
            self.portfolio_value.set(f"${self.portfolio_value_num:,.2f}")

            # Couleur du P&L
            pnl_color = "lightgreen" if self.daily_pnl_num >= 0 else "red"
            self.daily_pnl.set(f"${self.daily_pnl_num:,.2f}")
            self.pnl_label.configure(text_color=pnl_color)

            # Win rate
            if self.trades_count > 0:
                win_rate = (self.successful_trades / self.trades_count) * 100
                self.win_rate.set(f"{win_rate:.1f}%")

            # Log du trade IA
            action = "✅ GAIN IA" if is_success else "❌ PERTE IA"
            confidence_text = f"(Conf: {decision['confidence']:.1%})"
            strength_text = f"(Force: {decision['strength']:.1%})"
            self.log_message(f"{action} {symbol}: {trade_pnl:+.2f}€ {confidence_text} {strength_text}")

            # Log des signaux IA
            signals = decision['signals']
            self.log_message(f"  📊 Tech: {signals['technical']:+.2f} | 🤖 ML: {signals['ml_prediction']:+.2f} | 📰 Sentiment: {signals['sentiment']:+.2f}")

        except Exception as e:
            print(f"Erreur mise à jour affichage: {e}")

    def optimize_ai(self):
        """Optimiser l'IA Lite"""
        try:
            self.log_message("⚡ Optimisation de l'IA Lite...")

            def optimize():
                time.sleep(2)
                # Simuler une optimisation
                ai_engine.confidence_level = min(0.9, ai_engine.confidence_level + 0.05)
                self.root.after(0, lambda: self.log_message("✅ IA Lite optimisée!"))
                self.root.after(0, lambda: messagebox.showinfo("Optimisation", "IA Lite optimisée avec succès!"))

            threading.Thread(target=optimize, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ Erreur optimisation: {e}")

    def analyze_market(self):
        """Analyser le marché avec l'IA Lite"""
        try:
            self.log_message("📊 Analyse de marché IA Lite...")

            def analyze():
                time.sleep(3)

                # Simuler une analyse complète
                analysis_result = """🔍 ANALYSE IA LITE COMPLÈTE:
• Tendance: Haussière modérée (RSI: 65)
• Volatilité: Moyenne (0.45)
• Sentiment: Positif (0.72)
• Patterns: 3 détectés (Doji, Support, Tendance)
• ML Prédiction: +1.8% (confiance: 73%)
• Signaux techniques: ACHAT modéré
• Recommandation IA: ACHAT avec prudence"""

                self.root.after(0, lambda: self.log_message("✅ Analyse IA Lite terminée!"))
                self.root.after(0, lambda: self.log_message(analysis_result))
                self.root.after(0, lambda: messagebox.showinfo("Analyse IA", "Analyse de marché IA Lite terminée!\nVoir les logs pour les détails."))

            threading.Thread(target=analyze, daemon=True).start()

        except Exception as e:
            self.log_message(f"❌ Erreur analyse: {e}")

    def reset_ai(self):
        """Reset de l'IA Lite"""
        try:
            result = messagebox.askyesno(
                "Reset IA Lite",
                "Voulez-vous vraiment reset l'IA Lite?\n\n"
                "Cela effacera tout l'apprentissage et remettra\n"
                "les paramètres par défaut."
            )

            if result:
                # Reset de l'IA
                global ai_engine
                ai_engine = AILiteEngine()

                # Reset des métriques
                self.trades_count = 0
                self.successful_trades = 0
                self.daily_pnl_num = 0.0

                # Reset de l'affichage
                self.total_trades.set("0")
                self.win_rate.set("0%")
                self.daily_pnl.set("$0.00")
                self.ai_confidence.set("70%")
                self.ml_accuracy.set("65%")

                self.log_message("🔄 IA Lite reset avec succès!")
                messagebox.showinfo("Reset", "IA Lite reset avec succès!")

        except Exception as e:
            self.log_message(f"❌ Erreur reset: {e}")

    def _update_patterns_list(self, text):
        """Mettre à jour la liste des patterns"""
        try:
            self.patterns_list.delete("1.0", "end")
            self.patterns_list.insert("1.0", text)
        except:
            pass

    def _update_predictions(self, text):
        """Mettre à jour les prédictions"""
        try:
            self.predictions_text.delete("1.0", "end")
            self.predictions_text.insert("1.0", text)
        except:
            pass

    def _update_strategy_params(self, text):
        """Mettre à jour les paramètres de stratégie"""
        try:
            self.strategy_params.delete("1.0", "end")
            self.strategy_params.insert("1.0", text)
        except:
            pass

    def log_message(self, message):
        """Ajouter un message au log"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.insert("end", log_entry)
            self.log_text.see("end")

            # Limiter le nombre de lignes
            lines = self.log_text.get("1.0", "end").split("\n")
            if len(lines) > 150:
                self.log_text.delete("1.0", "20.0")

        except Exception as e:
            print(f"Erreur log: {e}")

    def clear_logs(self):
        """Effacer les logs"""
        try:
            self.log_text.delete("1.0", "end")
            self.log_message("🗑️ Logs effacés")
        except:
            pass

    def start_update_loops(self):
        """Démarrer les boucles de mise à jour"""
        def update_loop():
            while True:
                try:
                    if self.bot_running and not self.bot_paused:
                        # Fluctuations du portfolio
                        if hasattr(self, 'portfolio_value_num'):
                            fluctuation = random.uniform(-0.1, 0.1)
                            self.portfolio_value_num += fluctuation
                            self.root.after(0, lambda: self.portfolio_value.set(f"${self.portfolio_value_num:,.2f}"))

                    time.sleep(5)
                except:
                    break

        threading.Thread(target=update_loop, daemon=True).start()

    def on_closing(self):
        """Gestionnaire de fermeture"""
        if self.bot_running:
            result = messagebox.askyesno(
                "Fermeture",
                "L'IA Lite est en cours d'exécution.\n"
                "Voulez-vous l'arrêter et fermer l'application?"
            )
            if result:
                self.stop_ai_bot()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """Lancer l'application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Messages de démarrage
        self.log_message("🤖 INVESTT AI Lite Trading Bot démarré")
        self.log_message("🧠 Super-Algorithme Lite initialisé")
        self.log_message("✅ Modules IA Lite disponibles:")
        self.log_message("  • Analyse technique simplifiée (RSI, MACD)")
        self.log_message("  • Prédictions ML légères (sans pandas/numpy)")
        self.log_message("  • Détection de patterns basique")
        self.log_message("  • Analyse de sentiment simulée")
        self.log_message("  • Apprentissage adaptatif intégré")
        self.log_message("💡 Configurez vos paramètres et démarrez l'IA Lite")
        self.log_message("🔬 Version optimisée pour PyInstaller")

        self.root.mainloop()


def main():
    """Point d'entrée principal"""
    try:
        app = AITradingAppLite()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur Critique", f"Erreur lors du démarrage de l'IA Lite:\n{e}")


if __name__ == "__main__":
    main()
