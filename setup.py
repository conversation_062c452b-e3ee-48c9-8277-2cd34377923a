"""
Setup script for INVESTT Trading Bot
"""
import os
import shutil
from pathlib import Path

def create_directories():
    """Create necessary directories"""
    directories = [
        'logs',
        'data',
        'backups',
        'tests'
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")

def setup_environment():
    """Setup environment file"""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    if not env_file.exists() and env_example.exists():
        shutil.copy(env_example, env_file)
        print("✓ Created .env file from template")
        print("⚠️  Please edit .env file with your API keys and settings")
    else:
        print("✓ .env file already exists")

def install_ta_lib():
    """Instructions for installing TA-Lib"""
    print("\n" + "="*60)
    print("TA-LIB INSTALLATION INSTRUCTIONS")
    print("="*60)
    print("TA-Lib is required for technical analysis.")
    print("\nFor Windows:")
    print("1. Download TA-Lib from: https://www.lfd.uci.edu/~gohlke/pythonlibs/#ta-lib")
    print("2. Install with: pip install TA_Lib-0.4.XX-cpXX-cpXXm-win_amd64.whl")
    print("\nFor Linux/Mac:")
    print("1. Install TA-Lib C library first")
    print("2. Then: pip install TA-Lib")
    print("\nAlternatively, you can skip TA-Lib and use pandas-ta:")
    print("pip install pandas-ta")
    print("="*60)

def main():
    """Main setup function"""
    print("🚀 Setting up INVESTT Trading Bot...")
    print("="*50)
    
    # Create directories
    create_directories()
    
    # Setup environment
    setup_environment()
    
    # TA-Lib instructions
    install_ta_lib()
    
    print("\n✅ Setup completed!")
    print("\nNext steps:")
    print("1. Edit .env file with your API keys")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Run tests: python -m pytest tests/")
    print("4. Start the bot: python main.py")
    print("5. View dashboard: streamlit run dashboard.py")

if __name__ == "__main__":
    main()
