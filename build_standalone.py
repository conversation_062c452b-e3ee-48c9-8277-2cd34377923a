"""
🚀 INVESTT Trading Bot - Build Standalone Ultra-Simple
Version sans aucune dépendance problématique
"""
import subprocess
import sys
import os
from pathlib import Path
import shutil

def print_header():
    """Afficher l'en-tête"""
    print("🚀 BUILD INVESTT STANDALONE")
    print("=" * 40)
    print("Création d'un EXE ultra-simple et stable")
    print("=" * 40 + "\n")

def install_pyinstaller():
    """Installer PyInstaller seulement"""
    print("📦 Installation de PyInstaller...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "pyinstaller", "customtkinter"],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ PyInstaller et CustomTkinter installés")
            return True
        else:
            print(f"❌ Erreur: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_minimal_spec():
    """Créer un fichier .spec minimal"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['investt_app_standalone.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['customtkinter', 'tkinter'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'pandas', 'numpy', 'matplotlib', 'scipy', 'sklearn',
        'tensorflow', 'torch', 'cv2', 'PIL.ImageTk'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='INVESTT_Trading_Bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("standalone.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ Fichier .spec minimal créé")

def build_exe():
    """Construire l'EXE"""
    print("\n🔨 Construction de l'EXE...")
    print("⏳ Patience, cela peut prendre 2-5 minutes...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "PyInstaller", "--clean", "standalone.spec"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✅ EXE créé avec succès!")
            
            exe_path = Path("dist/INVESTT_Trading_Bot.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Fichier: {exe_path}")
                print(f"📊 Taille: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Fichier EXE non trouvé")
                return False
        else:
            print(f"❌ Erreur PyInstaller:")
            # Afficher seulement les erreurs importantes
            stderr_lines = result.stderr.split('\n')
            for line in stderr_lines:
                if 'ERROR' in line or 'CRITICAL' in line:
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout - Le build a pris trop de temps")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_final_package():
    """Créer le package final"""
    print("\n📦 Création du package final...")
    
    try:
        # Créer le dossier final
        final_folder = Path("INVESTT_Final")
        if final_folder.exists():
            shutil.rmtree(final_folder)
        
        final_folder.mkdir()
        
        # Copier l'EXE
        exe_source = Path("dist/INVESTT_Trading_Bot.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, final_folder / "INVESTT_Trading_Bot.exe")
            print("✅ EXE copié")
        
        # Créer un fichier .env par défaut
        env_content = '''# INVESTT Trading Bot - Configuration
# Éditez ce fichier avec vos vraies clés API pour le live trading

BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=true

# Paramètres de Trading (Modifiables dans l'interface)
INITIAL_CAPITAL=1000.0
MAX_POSITION_SIZE=0.02
MAX_DAILY_LOSS=150.0

# Paramètres de Stratégie
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0

# Mode (Modifiable dans l'interface)
PAPER_TRADING=true
LIVE_TRADING=false
'''
        
        with open(final_folder / ".env", "w") as f:
            f.write(env_content)
        print("✅ Fichier .env créé")
        
        # Créer un guide rapide
        guide_content = '''🚀 INVESTT TRADING BOT - GUIDE RAPIDE

═══════════════════════════════════════════════════════════════

🎯 DÉMARRAGE IMMÉDIAT:
   1. Double-cliquez sur "INVESTT_Trading_Bot.exe"
   2. L'interface moderne s'ouvre automatiquement
   3. Cliquez sur "DÉMARRER" pour la démo

⚙️ CONFIGURATION LIVE TRADING:
   1. Obtenez vos clés API sur binance.com
   2. Saisissez-les dans l'interface (section "Clés API")
   3. Basculez le switch "LIVE TRADING"
   4. Confirmez et démarrez

🛡️ SÉCURITÉ:
   ⚠️ Commencez TOUJOURS en Paper Trading
   ⚠️ Testez d'abord sur Binance Testnet
   ⚠️ Ne risquez jamais plus que vous pouvez perdre

📊 INTERFACE:
   - 💰 Switch Live/Paper Trading
   - 🚀 Boutons Start/Stop/Pause
   - 📈 Métriques temps réel
   - 📝 Logs d'activité
   - ⚙️ Configuration des paramètres
   - 🔑 Saisie des clés API

🔧 OBTENIR LES CLÉS API BINANCE:
   1. Créez un compte sur binance.com
   2. Allez dans Profil > Sécurité API
   3. Créez une nouvelle API avec permissions:
      ✅ Lecture des informations
      ✅ Trading au comptant
      ❌ Retrait (JAMAIS!)
   4. Copiez les clés dans l'interface

💡 CONSEILS:
   - Surveillez les performances régulièrement
   - Commencez avec de petits montants
   - Utilisez les limites de risque
   - Arrêt d'urgence toujours disponible

═══════════════════════════════════════════════════════════════
BON TRADING ! 🚀💰
'''
        
        with open(final_folder / "GUIDE_RAPIDE.txt", "w", encoding='utf-8') as f:
            f.write(guide_content)
        print("✅ Guide rapide créé")
        
        # Créer un lanceur batch (optionnel)
        batch_content = '''@echo off
title INVESTT Trading Bot
echo.
echo 🚀 Lancement d'INVESTT Trading Bot...
echo.
start "" "INVESTT_Trading_Bot.exe"
echo ✅ Application lancée!
timeout /t 2 >nul
'''
        
        with open(final_folder / "Lancer_INVESTT.bat", "w") as f:
            f.write(batch_content)
        print("✅ Lanceur batch créé")
        
        print(f"\n✅ Package final créé: {final_folder}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création package: {e}")
        return False

def main():
    """Fonction principale"""
    print_header()
    
    # Vérifier que le fichier source existe
    if not Path("investt_app_standalone.py").exists():
        print("❌ Fichier investt_app_standalone.py non trouvé!")
        print("💡 Assurez-vous d'être dans le bon dossier")
        input("Appuyez sur Entrée pour fermer...")
        return
    
    # Étapes de build
    steps = [
        ("Installation PyInstaller", install_pyinstaller),
        ("Création fichier .spec", create_minimal_spec),
        ("Construction EXE", build_exe),
        ("Création package final", create_final_package)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"🔄 {step_name}...")
        try:
            if step_func in [install_pyinstaller, build_exe, create_final_package]:
                success = step_func()
                if success:
                    success_count += 1
                elif step_func == build_exe:
                    print("💡 Essayez de relancer le script si l'erreur persiste")
                    break
            else:
                step_func()
                success_count += 1
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    # Résumé final
    print("\n" + "=" * 50)
    print("🎉 BUILD TERMINÉ!")
    print("=" * 50)
    
    final_exe = Path("INVESTT_Final/INVESTT_Trading_Bot.exe")
    if final_exe.exists():
        print("✅ EXE STANDALONE CRÉÉ AVEC SUCCÈS!")
        print(f"\n📁 Dossier: INVESTT_Final/")
        print(f"🚀 Fichier: INVESTT_Trading_Bot.exe")
        print(f"📊 Taille: {final_exe.stat().st_size / (1024*1024):.1f} MB")
        
        print("\n🎯 UTILISATION:")
        print("   1. Allez dans le dossier 'INVESTT_Final'")
        print("   2. Double-cliquez sur 'INVESTT_Trading_Bot.exe'")
        print("   3. Interface moderne avec simulation intégrée")
        print("   4. Pour live trading: ajoutez vos clés API")
        print("   5. Basculez le switch et confirmez")
        
        print("\n💡 AVANTAGES:")
        print("   ✅ Aucune installation Python requise")
        print("   ✅ Fonctionne sur n'importe quel PC Windows")
        print("   ✅ Interface ultra-moderne")
        print("   ✅ Simulation réaliste intégrée")
        print("   ✅ Configuration live trading simple")
        print("   ✅ Portable - copiez le dossier n'importe où")
        
    else:
        print("⚠️ EXE non créé")
        print(f"✅ Étapes réussies: {success_count}/{len(steps)}")
        print("\n💡 Conseils de dépannage:")
        print("   - Vérifiez que Python 3.8+ est installé")
        print("   - Relancez le script en tant qu'administrateur")
        print("   - Désactivez temporairement l'antivirus")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Build interrompu par l'utilisateur")
    except Exception as e:
        print(f"\n\n❌ Erreur critique: {e}")
    finally:
        input("\nAppuyez sur Entrée pour fermer...")
