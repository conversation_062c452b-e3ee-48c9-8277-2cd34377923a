# Core dependencies
pandas>=2.0.0
numpy>=1.24.0
python-dotenv>=1.0.0
pydantic>=2.0.0
loguru>=0.7.0

# Trading and market data
ccxt>=4.0.0
# ta-lib>=0.4.0  # Install manually or use pandas-ta as alternative
pandas-ta>=0.3.14b0
yfinance>=0.2.0
websocket-client>=1.6.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0
psycopg2-binary>=2.9.0

# API and web
fastapi>=0.100.0
uvicorn>=0.23.0
requests>=2.31.0
aiohttp>=3.8.0

# Machine Learning
scikit-learn>=1.3.0
tensorflow>=2.13.0
joblib>=1.3.0

# Visualization and monitoring
plotly>=5.15.0
streamlit>=1.25.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Desktop GUI
customtkinter>=5.2.0
tkinter-tooltip>=2.0.0
pillow>=10.0.0
ttkbootstrap>=1.10.0

# Notifications
python-telegram-bot>=20.0
smtplib-ssl>=1.0.0

# Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.11.0

# Development
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0
