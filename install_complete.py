"""
🚀 INVESTT Trading Bot - Installateur Complet
Installation automatique avec interface moderne
"""
import subprocess
import sys
import os
import shutil
from pathlib import Path
import time

def print_header():
    """Afficher l'en-tête stylé"""
    print("\n" + "="*60)
    print("🚀 INVESTT TRADING BOT - INSTALLATEUR COMPLET")
    print("="*60)
    print("Installation automatique de votre bot de trading IA")
    print("="*60 + "\n")

def check_python():
    """Vérifier la version de Python"""
    print("🐍 Vérification de Python...")
    
    try:
        version = sys.version_info
        if version.major < 3 or (version.major == 3 and version.minor < 8):
            print("❌ Python 3.8+ requis")
            print("📥 Téléchargez Python depuis: https://python.org")
            return False
        
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} détecté")
        return True
        
    except Exception as e:
        print(f"❌ Erreur vérification Python: {e}")
        return False

def install_dependencies():
    """Installer toutes les dépendances"""
    print("\n📦 Installation des dépendances...")
    
    # Dépendances essentielles
    essential_deps = [
        "customtkinter>=5.2.0",
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "python-dotenv>=1.0.0",
        "loguru>=0.7.0",
        "ccxt>=4.0.0",
        "pandas-ta>=0.3.14b0",
        "websocket-client>=1.6.0",
        "sqlalchemy>=2.0.0",
        "plotly>=5.15.0",
        "pillow>=10.0.0"
    ]
    
    # Dépendances optionnelles
    optional_deps = [
        "streamlit>=1.25.0",
        "matplotlib>=3.7.0",
        "pytest>=7.4.0",
        "pywin32",
        "winshell"
    ]
    
    success_count = 0
    total_deps = len(essential_deps) + len(optional_deps)
    
    # Installer les dépendances essentielles
    print("📋 Installation des dépendances essentielles...")
    for i, dep in enumerate(essential_deps, 1):
        try:
            print(f"   [{i}/{len(essential_deps)}] {dep.split('>=')[0]}...", end=" ")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dep],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅")
                success_count += 1
            else:
                print("❌")
                print(f"      Erreur: {result.stderr[:100]}...")
                
        except subprocess.TimeoutExpired:
            print("⏱️ Timeout")
        except Exception as e:
            print(f"❌ {e}")
    
    # Installer les dépendances optionnelles
    print("\n📋 Installation des dépendances optionnelles...")
    for i, dep in enumerate(optional_deps, 1):
        try:
            print(f"   [{i}/{len(optional_deps)}] {dep.split('>=')[0] if '>=' in dep else dep}...", end=" ")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dep],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print("✅")
                success_count += 1
            else:
                print("⚠️ (optionnel)")
                
        except:
            print("⚠️ (optionnel)")
    
    print(f"\n📊 Installation terminée: {success_count}/{total_deps} paquets installés")
    
    # Vérifier les dépendances critiques
    critical_imports = [
        ("customtkinter", "Interface graphique"),
        ("pandas", "Analyse de données"),
        ("ccxt", "Connecteur exchange"),
        ("sqlalchemy", "Base de données")
    ]
    
    print("\n🔍 Vérification des imports critiques...")
    all_critical_ok = True
    
    for module, description in critical_imports:
        try:
            __import__(module)
            print(f"   ✅ {module} ({description})")
        except ImportError:
            print(f"   ❌ {module} ({description}) - CRITIQUE")
            all_critical_ok = False
    
    return all_critical_ok

def setup_directories():
    """Créer les répertoires nécessaires"""
    print("\n📁 Création des répertoires...")
    
    directories = [
        "logs",
        "data", 
        "backups",
        "assets"
    ]
    
    for directory in directories:
        try:
            Path(directory).mkdir(exist_ok=True)
            print(f"   ✅ {directory}/")
        except Exception as e:
            print(f"   ❌ {directory}/ - {e}")

def setup_configuration():
    """Configurer les fichiers de configuration"""
    print("\n⚙️ Configuration des fichiers...")
    
    # Créer .env s'il n'existe pas
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        try:
            shutil.copy(env_example, env_file)
            print("   ✅ Fichier .env créé")
        except Exception as e:
            print(f"   ❌ Erreur création .env: {e}")
    elif env_file.exists():
        print("   ✅ Fichier .env existe déjà")
    else:
        # Créer un .env basique
        basic_config = """# INVESTT Trading Bot Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=true

# Trading Parameters
INITIAL_CAPITAL=1000.0
MAX_POSITION_SIZE=0.02
MAX_DAILY_LOSS=150.0
MAX_DRAWDOWN=0.10

# Strategy Parameters
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0

# Mode
PAPER_TRADING=true
LIVE_TRADING=false

# Logging
LOG_LEVEL=INFO
"""
        try:
            with open(env_file, 'w') as f:
                f.write(basic_config)
            print("   ✅ Fichier .env créé avec configuration par défaut")
        except Exception as e:
            print(f"   ❌ Erreur création .env: {e}")

def test_installation():
    """Tester l'installation"""
    print("\n🧪 Test de l'installation...")
    
    try:
        # Test d'import de l'application
        print("   🔍 Test d'import de l'application...", end=" ")
        
        # Ajouter le répertoire courant au path
        current_dir = str(Path(__file__).parent.absolute())
        if current_dir not in sys.path:
            sys.path.insert(0, current_dir)
        
        # Tester les imports principaux
        import customtkinter
        from config import config
        print("✅")
        
        # Test de la base de données
        print("   🔍 Test de la base de données...", end=" ")
        from src.database import init_db
        init_db()
        print("✅")
        
        print("   🎉 Tous les tests passés!")
        return True
        
    except Exception as e:
        print(f"❌")
        print(f"   Erreur: {e}")
        return False

def create_shortcuts():
    """Créer les raccourcis"""
    print("\n🔗 Création des raccourcis...")
    
    try:
        # Exécuter le script de création de raccourcis
        result = subprocess.run(
            [sys.executable, "create_shortcut.py"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("   ✅ Raccourcis créés")
        else:
            print("   ⚠️ Raccourcis non créés (optionnel)")
            
    except Exception as e:
        print(f"   ⚠️ Erreur raccourcis: {e}")

def show_completion_info():
    """Afficher les informations de fin d'installation"""
    print("\n" + "="*60)
    print("🎉 INSTALLATION TERMINÉE AVEC SUCCÈS!")
    print("="*60)
    
    print("\n🚀 Comment lancer INVESTT Trading Bot:")
    print("   1. Double-clic sur le raccourci bureau 'INVESTT Trading Bot'")
    print("   2. OU double-clic sur 'launch_investt.bat'")
    print("   3. OU exécuter: python investt_app.py")
    
    print("\n⚙️ Configuration:")
    print("   1. Éditez le fichier .env avec vos clés API Binance")
    print("   2. Commencez TOUJOURS en Paper Trading")
    print("   3. Testez sur Binance Testnet avant le live")
    
    print("\n🛡️ Sécurité:")
    print("   ⚠️ COMMENCEZ EN PAPER TRADING")
    print("   ⚠️ Utilisez le testnet pour les premiers tests")
    print("   ⚠️ Ne risquez jamais plus que vous pouvez perdre")
    
    print("\n📚 Documentation:")
    print("   - README.md: Guide complet")
    print("   - STRATEGIES.md: Guide des stratégies")
    print("   - PROJECT_STATUS.md: État du projet")
    
    print("\n" + "="*60)
    print("💰 PRÊT POUR LE TRADING AUTOMATISÉ!")
    print("="*60)

def main():
    """Installation principale"""
    print_header()
    
    # Étapes d'installation
    steps = [
        ("Vérification Python", check_python),
        ("Installation dépendances", install_dependencies),
        ("Création répertoires", setup_directories),
        ("Configuration fichiers", setup_configuration),
        ("Test installation", test_installation),
        ("Création raccourcis", create_shortcuts)
    ]
    
    failed_steps = []
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_func == install_dependencies or step_func == test_installation:
                success = step_func()
                if not success and step_func == install_dependencies:
                    failed_steps.append(step_name)
                elif not success and step_func == test_installation:
                    failed_steps.append(step_name)
            else:
                step_func()
                
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
            failed_steps.append(step_name)
    
    # Résumé
    if not failed_steps:
        show_completion_info()
    else:
        print(f"\n⚠️ Installation terminée avec {len(failed_steps)} problème(s):")
        for step in failed_steps:
            print(f"   - {step}")
        print("\n💡 L'application peut quand même fonctionner.")
        print("   Consultez la documentation pour résoudre les problèmes.")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Installation interrompue par l'utilisateur")
    except Exception as e:
        print(f"\n\n❌ Erreur critique: {e}")
    finally:
        input("\nAppuyez sur Entrée pour fermer...")
