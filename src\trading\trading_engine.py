"""
Main trading engine that orchestrates all trading operations
"""
import asyncio
import threading
import time
from typing import Dict, List, Optional, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass

from config import config
from src.utils.logger import log_trade, log_error, log_performance, logger
from src.data import market_data_manager, Ticker
from src.strategies import BaseStrategy, Signal
from src.risk import risk_manager, RiskCheck
from src.database import session_scope, Trade, Order, Position, PerformanceMetrics
from .order_manager import OrderManager, OrderRequest
from .portfolio_manager import PortfolioManager


@dataclass
class TradingState:
    """Current state of the trading engine"""
    is_running: bool = False
    is_paused: bool = False
    start_time: Optional[datetime] = None
    total_trades: int = 0
    successful_trades: int = 0
    failed_trades: int = 0
    current_pnl: float = 0.0


class TradingEngine:
    """Main trading engine that coordinates all trading activities"""
    
    def __init__(self):
        self.state = TradingState()
        self.strategies: List[BaseStrategy] = []
        self.signal_handlers: List[Callable[[Signal], None]] = []
        
        # Core components
        self.order_manager = OrderManager()
        self.portfolio_manager = PortfolioManager()
        
        # Threading
        self.main_thread = None
        self.stop_event = threading.Event()
        
        # Performance tracking
        self.performance_metrics = {
            'daily_pnl': 0.0,
            'total_pnl': 0.0,
            'win_rate': 0.0,
            'sharpe_ratio': 0.0,
            'max_drawdown': 0.0
        }
        
        self.initialize()
    
    def initialize(self):
        """Initialize the trading engine"""
        logger.info("Initializing trading engine...")
        
        # Initialize components
        self.portfolio_manager.initialize()
        
        # Register signal handler
        self.add_signal_handler(self._handle_signal)
        
        logger.info("Trading engine initialized")
    
    def add_strategy(self, strategy: BaseStrategy):
        """Add a trading strategy"""
        self.strategies.append(strategy)
        
        # Connect strategy to signal handling
        strategy._notify_signal_handlers = lambda signal: self._emit_signal(signal)
        
        logger.info(f"Added strategy: {strategy.name}")
    
    def remove_strategy(self, strategy_name: str):
        """Remove a trading strategy"""
        self.strategies = [s for s in self.strategies if s.name != strategy_name]
        logger.info(f"Removed strategy: {strategy_name}")
    
    def add_signal_handler(self, handler: Callable[[Signal], None]):
        """Add a signal handler"""
        self.signal_handlers.append(handler)
    
    def _emit_signal(self, signal: Signal):
        """Emit a signal to all handlers"""
        for handler in self.signal_handlers:
            try:
                handler(signal)
            except Exception as e:
                log_error(f"Error in signal handler", e)
    
    def _handle_signal(self, signal: Signal):
        """Main signal handling logic"""
        try:
            log_trade(f"Processing signal: {signal.action.upper()} {signal.symbol} at {signal.price}")
            
            # Get current portfolio value
            portfolio_value = self.portfolio_manager.get_total_value()
            
            # Risk check
            risk_check = risk_manager.check_signal_risk(signal, portfolio_value)
            
            if not risk_check.passed:
                log_trade(f"Signal rejected by risk manager: {risk_check.reason}")
                return
            
            # Calculate position size
            position_size = risk_manager.calculate_position_size(
                signal, portfolio_value, signal.price
            )
            
            # Create order request
            order_request = OrderRequest(
                symbol=signal.symbol,
                side=signal.action,
                order_type='market',
                amount=position_size.amount,
                price=signal.price,
                strategy_name=signal.strategy_name,
                metadata=signal.metadata
            )
            
            # Execute order
            if config.PAPER_TRADING:
                self._execute_paper_trade(order_request, signal)
            else:
                self._execute_live_trade(order_request, signal)
                
        except Exception as e:
            log_error(f"Error handling signal for {signal.symbol}", e)
    
    def _execute_paper_trade(self, order_request: OrderRequest, signal: Signal):
        """Execute a paper trade (simulation)"""
        try:
            # Simulate order execution
            trade = Trade(
                symbol=order_request.symbol,
                side=order_request.side,
                amount=order_request.amount,
                price=order_request.price,
                value=order_request.amount * order_request.price,
                fee=order_request.amount * order_request.price * 0.001,  # 0.1% fee
                strategy_name=order_request.strategy_name,
                metadata=order_request.metadata
            )
            
            # Save to database
            with session_scope() as session:
                session.add(trade)
                session.commit()
            
            # Update portfolio
            self.portfolio_manager.update_position(
                order_request.symbol,
                order_request.side,
                order_request.amount,
                order_request.price
            )
            
            # Update risk manager
            risk_manager.update_position(
                order_request.symbol,
                order_request.side,
                order_request.amount,
                order_request.price
            )
            
            # Update strategy
            for strategy in self.strategies:
                if strategy.name == order_request.strategy_name:
                    strategy.update_position(
                        order_request.symbol,
                        'long' if order_request.side == 'buy' else 'short',
                        order_request.price,
                        order_request.amount
                    )
                    break
            
            self.state.total_trades += 1
            
            log_trade(
                f"Paper trade executed: {order_request.side.upper()} {order_request.amount:.6f} "
                f"{order_request.symbol} at {order_request.price:.2f}"
            )
            
        except Exception as e:
            log_error(f"Error executing paper trade", e)
            self.state.failed_trades += 1
    
    def _execute_live_trade(self, order_request: OrderRequest, signal: Signal):
        """Execute a live trade"""
        try:
            # Use order manager to execute the trade
            order_id = self.order_manager.place_order(order_request)
            
            if order_id:
                self.state.total_trades += 1
                log_trade(f"Live order placed: {order_id}")
            else:
                self.state.failed_trades += 1
                log_error("Failed to place live order")
                
        except Exception as e:
            log_error(f"Error executing live trade", e)
            self.state.failed_trades += 1
    
    def start(self):
        """Start the trading engine"""
        if self.state.is_running:
            logger.warning("Trading engine is already running")
            return
        
        self.state.is_running = True
        self.state.start_time = datetime.utcnow()
        self.stop_event.clear()
        
        # Start market data manager
        market_data_manager.start()
        
        # Subscribe to market data for all symbols
        for symbol in config.TRADING_PAIRS:
            market_data_manager.subscribe_to_ticker(symbol, self._on_ticker_update)
        
        # Start main trading loop
        self.main_thread = threading.Thread(target=self._main_loop, daemon=True)
        self.main_thread.start()
        
        logger.info("Trading engine started")
    
    def stop(self):
        """Stop the trading engine"""
        if not self.state.is_running:
            logger.warning("Trading engine is not running")
            return
        
        self.state.is_running = False
        self.stop_event.set()
        
        # Stop market data manager
        market_data_manager.stop()
        
        # Wait for main thread to finish
        if self.main_thread and self.main_thread.is_alive():
            self.main_thread.join(timeout=5)
        
        logger.info("Trading engine stopped")
    
    def pause(self):
        """Pause trading (stop generating new signals)"""
        self.state.is_paused = True
        logger.info("Trading engine paused")
    
    def resume(self):
        """Resume trading"""
        self.state.is_paused = False
        logger.info("Trading engine resumed")
    
    def _main_loop(self):
        """Main trading loop"""
        logger.info("Starting main trading loop")
        
        while self.state.is_running and not self.stop_event.is_set():
            try:
                if not self.state.is_paused:
                    # Update performance metrics
                    self._update_performance_metrics()
                    
                    # Check for strategy signals based on historical data
                    self._check_strategy_signals()
                
                # Sleep for a short interval
                time.sleep(1)
                
            except Exception as e:
                log_error("Error in main trading loop", e)
                time.sleep(5)  # Wait longer on error
        
        logger.info("Main trading loop stopped")
    
    def _on_ticker_update(self, ticker: Ticker):
        """Handle real-time ticker updates"""
        if self.state.is_paused:
            return
        
        try:
            # Update portfolio with current prices
            self.portfolio_manager.update_current_price(ticker.symbol, ticker.last)
            
            # Notify strategies of ticker update
            for strategy in self.strategies:
                strategy.on_ticker_update(ticker)
                
        except Exception as e:
            log_error(f"Error processing ticker update for {ticker.symbol}", e)
    
    def _check_strategy_signals(self):
        """Check strategies for new signals based on historical data"""
        for strategy in self.strategies:
            if not strategy.enabled:
                continue
            
            for symbol in strategy.symbols:
                try:
                    # Get historical data
                    data = market_data_manager.get_historical_data(
                        symbol, strategy.timeframe, limit=100
                    )
                    
                    if data.empty:
                        continue
                    
                    # Get current price
                    current_price = market_data_manager.get_current_price(symbol)
                    if not current_price:
                        continue
                    
                    # Let strategy analyze data
                    strategy.on_data_update(symbol, data, current_price)
                    
                except Exception as e:
                    log_error(f"Error checking signals for {symbol} in {strategy.name}", e)
    
    def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            # Get current portfolio value
            total_value = self.portfolio_manager.get_total_value()
            daily_pnl = self.portfolio_manager.get_daily_pnl()
            
            # Update state
            self.state.current_pnl = daily_pnl
            
            # Calculate win rate
            if self.state.total_trades > 0:
                win_rate = self.state.successful_trades / self.state.total_trades
            else:
                win_rate = 0.0
            
            self.performance_metrics.update({
                'daily_pnl': daily_pnl,
                'total_pnl': total_value - config.INITIAL_CAPITAL,
                'win_rate': win_rate,
                'total_trades': self.state.total_trades
            })
            
        except Exception as e:
            log_error("Error updating performance metrics", e)
    
    def get_status(self) -> Dict:
        """Get current trading engine status"""
        return {
            'is_running': self.state.is_running,
            'is_paused': self.state.is_paused,
            'start_time': self.state.start_time,
            'uptime': (datetime.utcnow() - self.state.start_time).total_seconds() if self.state.start_time else 0,
            'total_trades': self.state.total_trades,
            'successful_trades': self.state.successful_trades,
            'failed_trades': self.state.failed_trades,
            'current_pnl': self.state.current_pnl,
            'strategies': len(self.strategies),
            'active_strategies': len([s for s in self.strategies if s.enabled]),
            'portfolio_value': self.portfolio_manager.get_total_value(),
            'risk_status': risk_manager.get_risk_status(),
            'performance': self.performance_metrics
        }


# Global trading engine instance
trading_engine = TradingEngine()


def get_trading_engine() -> TradingEngine:
    """Get the global trading engine"""
    return trading_engine
