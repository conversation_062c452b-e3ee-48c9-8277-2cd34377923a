# 🚀 INVESTT - Agent IA de Trading Automatisé

INVESTT est un bot de trading automatisé intelligent qui utilise des stratégies d'analyse technique pour trader sur les marchés des cryptomonnaies et du Forex. Le système est conçu pour être robuste, sécurisé et hautement configurable.

## ✨ Fonctionnalités Principales

### 🎯 Stratégies de Trading
- **Stratégie RSI** : Trading basé sur l'indicateur RSI avec détection de surachat/survente
- **Gestion des signaux** : Système de signaux avec force et métadonnées
- **Support multi-timeframes** : 1m, 5m, 15m, 1h, etc.
- **Extensible** : Architecture modulaire pour ajouter facilement de nouvelles stratégies

### 🛡️ Gestion des Risques Avancée
- **Limite de perte quotidienne** : Protection contre les pertes excessives
- **Taille de position dynamique** : Calcul automatique basé sur le risque
- **Drawdown maximum** : Surveillance continue du drawdown
- **Corrélation des positions** : Évite la sur-exposition
- **Filtres de volatilité** : Pause automatique en cas de volatilité excessive

### 📊 Monitoring et Analytics
- **Dashboard temps réel** : Interface Streamlit interactive
- **Métriques de performance** : P&L, win rate, Sharpe ratio
- **Historique des trades** : Suivi complet des opérations
- **Alertes et notifications** : Système d'alertes configurable
- **Logs détaillés** : Logging avancé pour audit et debug

### 🔌 Connectivité Marchés
- **Binance** : Support complet avec API REST et WebSocket
- **Paper Trading** : Mode simulation pour tests sans risque
- **Données temps réel** : Flux de prix en temps réel
- **Gestion des ordres** : Système d'ordres robuste avec retry

## 🏗️ Architecture Technique

```
INVESTT/
├── src/
│   ├── data/           # Gestion des données de marché
│   ├── strategies/     # Stratégies de trading
│   ├── trading/        # Moteur de trading et gestion des ordres
│   ├── risk/          # Gestion des risques
│   ├── database/      # Modèles et gestion BDD
│   └── utils/         # Utilitaires (logging, etc.)
├── config.py          # Configuration centralisée
├── main.py           # Point d'entrée principal
├── dashboard.py      # Interface de monitoring
└── requirements.txt  # Dépendances
```

## 🚀 Installation Ultra-Simple

### 🎯 Installation Automatique (Recommandée)
```bash
# 1. Télécharger le projet
# 2. Double-clic sur: install_complete.py
# 3. Suivre les instructions
# 4. C'est tout ! 🎉
```

### 🖥️ Lancement de l'Application Desktop
```bash
# Méthode 1: Raccourci bureau (après installation)
Double-clic sur "INVESTT Trading Bot"

# Méthode 2: Script de lancement
Double-clic sur "launch_investt.bat"

# Méthode 3: Ligne de commande
python investt_app.py
```

### ⚙️ Configuration Rapide
1. **Éditer le fichier .env** avec vos clés API Binance
2. **Choisir le mode** : Paper Trading (sûr) ou Live Trading (argent réel)
3. **Configurer les limites** de risque dans l'interface
4. **Cliquer sur DÉMARRER** ! 🚀

### 4. Configuration des clés API Binance

1. Créer un compte sur [Binance](https://binance.com)
2. Générer des clés API dans les paramètres de sécurité
3. Pour les tests, utiliser [Binance Testnet](https://testnet.binance.vision/)
4. Ajouter les clés dans le fichier `.env`

```env
BINANCE_API_KEY=votre_cle_api
BINANCE_SECRET_KEY=votre_cle_secrete
BINANCE_TESTNET=true  # false pour le trading live
```

## 🎮 Utilisation

### 🖥️ Interface Desktop Moderne
L'application dispose d'une **interface graphique ultra-moderne** avec :
- **Contrôles en temps réel** : Start/Stop/Pause d'un clic
- **Monitoring live** : Portfolio, P&L, trades en temps réel
- **Configuration intuitive** : Paramètres modifiables dans l'interface
- **Logs en direct** : Surveillance complète de l'activité
- **Mode Live/Paper** : Basculement sécurisé entre les modes

### 🚀 Démarrage Rapide
1. **Double-clic** sur le raccourci bureau "INVESTT Trading Bot"
2. **Configurer** vos paramètres dans l'interface
3. **Basculer** en Live Trading si désiré (⚠️ argent réel)
4. **Cliquer** sur "🚀 DÉMARRER"
5. **Surveiller** les performances en temps réel

### 📊 Dashboard Web (Optionnel)
```bash
# Pour une vue web complémentaire
streamlit run dashboard.py
```

### 🧪 Tests
```bash
# Tester l'installation
python -m pytest tests/ -v
```

## ⚙️ Configuration Avancée

### Paramètres de Trading
```env
# Capital initial
INITIAL_CAPITAL=1000.0

# Gestion des risques
MAX_POSITION_SIZE=0.02      # 2% par trade
MAX_DAILY_LOSS=150.0        # Perte max par jour
MAX_DRAWDOWN=0.10           # Drawdown max 10%

# Stratégie RSI
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0
```

### Paires de Trading
```env
TRADING_PAIRS=BTC/USDT,ETH/USDT,BNB/USDT
PRIMARY_TIMEFRAME=5m
```

## 📈 Stratégies Disponibles

### Stratégie RSI
- **Principe** : Mean reversion basé sur l'indicateur RSI
- **Signaux d'achat** : RSI < 30 (survente)
- **Signaux de vente** : RSI > 70 (surachat)
- **Filtres** : Volume, tendance, proximité des extremums
- **Sortie** : Take profit 3%, Stop loss 2%

### Ajout de Nouvelles Stratégies
```python
from src.strategies import BaseStrategy, Signal

class MaStrategy(BaseStrategy):
    def generate_signal(self, symbol, data, current_price):
        # Votre logique de stratégie
        return Signal(...)
```

## 🛡️ Sécurité et Bonnes Pratiques

### Recommandations de Sécurité
1. **Commencer en paper trading** : Toujours tester d'abord
2. **Utiliser le testnet** : Pour les tests avec API réelles
3. **Limiter les permissions API** : Seulement trading, pas de withdrawal
4. **Surveiller régulièrement** : Vérifier les performances quotidiennement
5. **Sauvegardes** : Sauvegarder la base de données régulièrement

### Gestion des Risques
- Ne jamais risquer plus que vous pouvez vous permettre de perdre
- Commencer avec un petit capital (1000-5000€)
- Diversifier sur plusieurs paires
- Surveiller les corrélations
- Respecter les limites de drawdown

## 📊 Métriques de Performance

### Métriques Suivies
- **P&L quotidien et total**
- **Win rate** : Pourcentage de trades gagnants
- **Profit factor** : Ratio gains/pertes
- **Sharpe ratio** : Rendement ajusté du risque
- **Maximum drawdown** : Perte maximale depuis un pic
- **Nombre de trades** : Volume d'activité

### Objectifs de Performance
- **Rendement mensuel** : 1-5% constant
- **Win rate** : > 60%
- **Sharpe ratio** : > 1.5
- **Drawdown max** : < 15%

## 🔧 Développement et Contribution

### Structure du Code
- **Modularité** : Chaque composant est indépendant
- **Tests** : Coverage complète avec pytest
- **Documentation** : Code documenté et typé
- **Logging** : Système de logs avancé
- **Configuration** : Centralisée et flexible

### Ajout de Fonctionnalités
1. Fork le repository
2. Créer une branche feature
3. Développer avec tests
4. Soumettre une pull request

## 📞 Support et Communauté

### Problèmes Courants
- **Erreur de connexion API** : Vérifier les clés et permissions
- **Données manquantes** : Vérifier la connexion internet
- **Performance lente** : Réduire le nombre de paires tradées

### Logs et Debug
```bash
# Logs principaux
tail -f logs/investt.log

# Logs de trading
tail -f logs/trading.log

# Logs d'erreurs
tail -f logs/errors.log
```

## ⚠️ Avertissements Légaux

- **Risque financier** : Le trading comporte des risques de perte
- **Pas de garantie** : Aucune garantie de profit
- **Responsabilité** : Utilisez à vos propres risques
- **Conformité** : Respectez les régulations locales
- **Tests** : Testez toujours avant le trading live

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.

---

**⚡ Développé avec passion pour la communauté des traders algorithmiques**

Pour plus d'informations, consultez la documentation complète ou contactez l'équipe de développement.
