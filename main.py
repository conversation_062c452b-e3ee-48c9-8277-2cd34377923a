"""
INVESTT Trading Bot - Main Entry Point
"""
import sys
import signal
import time
from datetime import datetime

from config import config
from src.utils.logger import logger, log_trade, log_error
from src.database import init_db
from src.trading import trading_engine
from src.strategies import create_rsi_strategy


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info("Shutdown signal received, stopping trading bot...")
    trading_engine.stop()
    sys.exit(0)


def setup_strategies():
    """Setup and configure trading strategies"""
    logger.info("Setting up trading strategies...")
    
    # Create RSI strategy
    rsi_strategy = create_rsi_strategy(
        symbols=config.TRADING_PAIRS,
        timeframe=config.PRIMARY_TIMEFRAME,
        rsi_period=config.RSI_PERIOD,
        oversold=config.RSI_OVERSOLD,
        overbought=config.RSI_OVERBOUGHT
    )
    
    # Add strategy to trading engine
    trading_engine.add_strategy(rsi_strategy)
    
    logger.info(f"Added {len(trading_engine.strategies)} strategies")


def validate_configuration():
    """Validate configuration before starting"""
    logger.info("Validating configuration...")
    
    # Check required API keys for live trading
    if config.LIVE_TRADING:
        if not config.BINANCE_API_KEY or not config.BINANCE_SECRET_KEY:
            logger.error("API keys required for live trading")
            return False
        
        logger.warning("LIVE TRADING MODE ENABLED - Real money will be used!")
        response = input("Are you sure you want to continue with live trading? (yes/no): ")
        if response.lower() != 'yes':
            logger.info("Live trading cancelled by user")
            return False
    
    # Check trading pairs
    if not config.TRADING_PAIRS:
        logger.error("No trading pairs configured")
        return False
    
    # Check capital
    if config.INITIAL_CAPITAL <= 0:
        logger.error("Initial capital must be positive")
        return False
    
    logger.info("Configuration validation passed")
    return True


def print_startup_info():
    """Print startup information"""
    print("\n" + "="*60)
    print("🚀 INVESTT TRADING BOT")
    print("="*60)
    print(f"Mode: {'LIVE TRADING' if config.LIVE_TRADING else 'PAPER TRADING'}")
    print(f"Exchange: Binance {'(Testnet)' if config.BINANCE_TESTNET else '(Live)'}")
    print(f"Initial Capital: ${config.INITIAL_CAPITAL:,.2f}")
    print(f"Trading Pairs: {', '.join(config.TRADING_PAIRS)}")
    print(f"Primary Timeframe: {config.PRIMARY_TIMEFRAME}")
    print(f"Max Daily Loss: ${config.MAX_DAILY_LOSS:,.2f}")
    print(f"Max Position Size: {config.MAX_POSITION_SIZE:.1%}")
    print(f"Started at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC")
    print("="*60)
    
    if config.PAPER_TRADING:
        print("📝 Running in PAPER TRADING mode - No real money at risk")
    else:
        print("⚠️  LIVE TRADING MODE - Real money is at risk!")
    
    print("="*60 + "\n")


def main():
    """Main function"""
    try:
        # Setup signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Print startup info
        print_startup_info()
        
        # Validate configuration
        if not validate_configuration():
            sys.exit(1)
        
        # Initialize database
        logger.info("Initializing database...")
        init_db()
        
        # Setup strategies
        setup_strategies()
        
        # Start trading engine
        logger.info("Starting trading engine...")
        trading_engine.start()
        
        log_trade("INVESTT Trading Bot started successfully")
        
        # Main loop - keep the bot running
        try:
            while True:
                # Print status every 60 seconds
                status = trading_engine.get_status()
                
                if status['total_trades'] > 0:
                    logger.info(
                        f"Status: Running | Trades: {status['total_trades']} | "
                        f"PnL: ${status['current_pnl']:.2f} | "
                        f"Portfolio: ${status['portfolio_value']:.2f}"
                    )
                
                time.sleep(60)  # Wait 1 minute
                
        except KeyboardInterrupt:
            logger.info("Keyboard interrupt received")
        
    except Exception as e:
        log_error("Critical error in main function", e)
        sys.exit(1)
    
    finally:
        # Cleanup
        logger.info("Shutting down trading bot...")
        trading_engine.stop()
        logger.info("Trading bot stopped")


if __name__ == "__main__":
    main()
