"""
RSI-based trading strategy
"""
import pandas as pd
import numpy as np
from typing import Optional, Dict, Any
from datetime import datetime

from .base_strategy import BaseStrategy, Signal, StrategyConfig
from src.utils.logger import log_strategy
from config import STRATEGY_CONFIGS


def calculate_rsi(prices: pd.Series, period: int = 14) -> pd.Series:
    """Calculate RSI indicator using pandas-ta or manual calculation"""
    try:
        # Try to use pandas-ta if available
        import pandas_ta as ta
        return ta.rsi(prices, length=period)
    except ImportError:
        # Fallback to manual calculation
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi


class RSIStrategy(BaseStrategy):
    """RSI-based mean reversion strategy"""
    
    def initialize(self):
        """Initialize RSI strategy parameters"""
        rsi_config = STRATEGY_CONFIGS.get('rsi_strategy', {})
        
        self.rsi_period = self.parameters.get('rsi_period', rsi_config.get('period', 14))
        self.oversold_threshold = self.parameters.get('oversold', rsi_config.get('oversold', 30))
        self.overbought_threshold = self.parameters.get('overbought', rsi_config.get('overbought', 70))
        self.min_data_points = self.rsi_period + 10  # Minimum data points needed
        
        # Exit thresholds (closer to neutral)
        self.exit_oversold = self.parameters.get('exit_oversold', 50)
        self.exit_overbought = self.parameters.get('exit_overbought', 50)
        
        # Additional filters
        self.volume_filter = self.parameters.get('volume_filter', True)
        self.trend_filter = self.parameters.get('trend_filter', False)
        
        log_strategy(
            self.name,
            f"Initialized with RSI period: {self.rsi_period}, "
            f"Oversold: {self.oversold_threshold}, Overbought: {self.overbought_threshold}"
        )
    
    def generate_signal(self, symbol: str, data: pd.DataFrame, current_price: float) -> Optional[Signal]:
        """Generate RSI-based trading signals"""
        if len(data) < self.min_data_points:
            return None
        
        # Calculate RSI
        rsi = calculate_rsi(data['close'], self.rsi_period)
        current_rsi = rsi.iloc[-1]
        
        if pd.isna(current_rsi):
            return None
        
        # Apply volume filter if enabled
        if self.volume_filter:
            avg_volume = data['volume'].rolling(window=20).mean().iloc[-1]
            current_volume = data['volume'].iloc[-1]
            
            if current_volume < avg_volume * 0.5:  # Low volume filter
                return None
        
        # Apply trend filter if enabled
        if self.trend_filter:
            sma_20 = data['close'].rolling(window=20).mean().iloc[-1]
            sma_50 = data['close'].rolling(window=50).mean().iloc[-1]
            
            if pd.isna(sma_20) or pd.isna(sma_50):
                return None
        
        # Generate signals
        signal = None
        action = 'hold'
        strength = 0.0
        metadata = {
            'rsi': current_rsi,
            'price': current_price,
            'volume_ratio': data['volume'].iloc[-1] / data['volume'].rolling(window=20).mean().iloc[-1] if self.volume_filter else 1.0
        }
        
        # Buy signal (oversold condition)
        if current_rsi <= self.oversold_threshold:
            action = 'buy'
            # Strength increases as RSI gets more oversold
            strength = min(1.0, (self.oversold_threshold - current_rsi) / self.oversold_threshold)
            
            # Additional confirmation: price should be near recent lows
            recent_low = data['low'].rolling(window=10).min().iloc[-1]
            if current_price <= recent_low * 1.02:  # Within 2% of recent low
                strength *= 1.2  # Boost signal strength
            
            metadata['signal_reason'] = 'oversold'
            metadata['strength_factors'] = {
                'rsi_oversold': (self.oversold_threshold - current_rsi) / self.oversold_threshold,
                'near_low': current_price <= recent_low * 1.02
            }
        
        # Sell signal (overbought condition)
        elif current_rsi >= self.overbought_threshold:
            action = 'sell'
            # Strength increases as RSI gets more overbought
            strength = min(1.0, (current_rsi - self.overbought_threshold) / (100 - self.overbought_threshold))
            
            # Additional confirmation: price should be near recent highs
            recent_high = data['high'].rolling(window=10).max().iloc[-1]
            if current_price >= recent_high * 0.98:  # Within 2% of recent high
                strength *= 1.2  # Boost signal strength
            
            metadata['signal_reason'] = 'overbought'
            metadata['strength_factors'] = {
                'rsi_overbought': (current_rsi - self.overbought_threshold) / (100 - self.overbought_threshold),
                'near_high': current_price >= recent_high * 0.98
            }
        
        # Only generate signal if strength is above threshold
        if action != 'hold' and strength >= 0.3:  # Minimum signal strength
            signal = Signal(
                symbol=symbol,
                action=action,
                strength=min(1.0, strength),  # Cap at 1.0
                price=current_price,
                timestamp=datetime.utcnow(),
                strategy_name=self.name,
                metadata=metadata
            )
        
        return signal
    
    def should_exit(self, symbol: str, current_price: float, position_info: Dict) -> bool:
        """Determine if position should be exited based on RSI"""
        # This would need current RSI data, which requires recent price data
        # For now, implement basic profit/loss exit logic
        
        entry_price = position_info['entry_price']
        side = position_info['side']
        
        if side == 'long':
            # Exit long position if price drops significantly or RSI indicates overbought
            profit_pct = (current_price - entry_price) / entry_price
            
            # Take profit at 3% or stop loss at -2%
            if profit_pct >= 0.03 or profit_pct <= -0.02:
                return True
                
        elif side == 'short':
            # Exit short position if price rises significantly or RSI indicates oversold
            profit_pct = (entry_price - current_price) / entry_price
            
            # Take profit at 3% or stop loss at -2%
            if profit_pct >= 0.03 or profit_pct <= -0.02:
                return True
        
        return False
    
    def get_rsi_analysis(self, symbol: str, data: pd.DataFrame) -> Dict[str, Any]:
        """Get detailed RSI analysis for a symbol"""
        if len(data) < self.min_data_points:
            return {'error': 'Insufficient data'}
        
        rsi = calculate_rsi(data['close'], self.rsi_period)
        current_rsi = rsi.iloc[-1]
        
        # RSI statistics
        rsi_stats = {
            'current': current_rsi,
            'mean': rsi.mean(),
            'std': rsi.std(),
            'min': rsi.min(),
            'max': rsi.max(),
            'oversold_count': (rsi <= self.oversold_threshold).sum(),
            'overbought_count': (rsi >= self.overbought_threshold).sum()
        }
        
        # Current market condition
        if current_rsi <= self.oversold_threshold:
            condition = 'oversold'
        elif current_rsi >= self.overbought_threshold:
            condition = 'overbought'
        elif current_rsi < 45:
            condition = 'bearish'
        elif current_rsi > 55:
            condition = 'bullish'
        else:
            condition = 'neutral'
        
        return {
            'symbol': symbol,
            'rsi_stats': rsi_stats,
            'condition': condition,
            'signal_strength': self._calculate_signal_strength(current_rsi),
            'parameters': {
                'period': self.rsi_period,
                'oversold': self.oversold_threshold,
                'overbought': self.overbought_threshold
            }
        }
    
    def _calculate_signal_strength(self, rsi: float) -> float:
        """Calculate signal strength based on RSI value"""
        if rsi <= self.oversold_threshold:
            return min(1.0, (self.oversold_threshold - rsi) / self.oversold_threshold)
        elif rsi >= self.overbought_threshold:
            return min(1.0, (rsi - self.overbought_threshold) / (100 - self.overbought_threshold))
        else:
            return 0.0


def create_rsi_strategy(symbols: list, **kwargs) -> RSIStrategy:
    """Factory function to create RSI strategy"""
    config = StrategyConfig(
        name="RSI_Strategy",
        symbols=symbols,
        timeframe=kwargs.get('timeframe', '5m'),
        parameters=kwargs
    )
    return RSIStrategy(config)
