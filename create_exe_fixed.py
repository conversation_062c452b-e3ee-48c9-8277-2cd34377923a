"""
🚀 INVESTT Trading Bot - Créateur d'EXE Corrigé
Version sans dépendances problématiques
"""
import subprocess
import sys
import os
from pathlib import Path
import shutil

def install_dependencies():
    """Installer les dépendances nécessaires"""
    print("📦 Installation des dépendances...")
    
    deps = [
        "pyinstaller",
        "customtkinter",
        "python-dotenv",
        "pillow"
    ]
    
    for dep in deps:
        try:
            print(f"   📦 {dep}...", end=" ")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dep],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅")
            else:
                print("❌")
                
        except Exception as e:
            print(f"❌ {e}")

def create_simple_icon():
    """Créer une icône simple"""
    print("🎨 Création de l'icône...")
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        # Créer une icône simple
        size = 256
        img = Image.new('RGBA', (size, size), (30, 144, 255, 255))  # Bleu
        draw = ImageDraw.Draw(img)
        
        # Cercle blanc
        margin = 30
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=4)
        
        # Texte "I"
        try:
            font = ImageFont.truetype("arial.ttf", 100)
        except:
            font = ImageFont.load_default()
        
        text = "I"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 10
        
        draw.text((x, y), text, fill=(30, 144, 255, 255), font=font)
        
        # Sauvegarder
        os.makedirs("assets", exist_ok=True)
        img.save("assets/icon.ico", format='ICO', sizes=[(256, 256), (64, 64), (32, 32), (16, 16)])
        print("✅ Icône créée")
        return True
        
    except Exception as e:
        print(f"⚠️ Erreur création icône: {e}")
        return False

def create_spec_file():
    """Créer un fichier .spec simplifié"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['investt_app_fixed.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config_simple.py', '.'),
        ('.env.example', '.'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'customtkinter',
        'tkinter',
        'PIL',
        'dotenv',
        'threading',
        'pathlib'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'pandas',
        'numpy', 
        'ccxt',
        'sqlalchemy',
        'loguru',
        'plotly',
        'websocket',
        'pydantic',
        'matplotlib',
        'seaborn'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='INVESTT_Trading_Bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open("investt_fixed.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ Fichier .spec créé")

def build_exe():
    """Construire l'EXE"""
    print("\n🔨 Construction de l'EXE...")
    print("⏳ Cela peut prendre quelques minutes...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "PyInstaller", "--clean", "investt_fixed.spec"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✅ EXE créé avec succès!")
            
            exe_path = Path("dist/INVESTT_Trading_Bot.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Fichier: {exe_path}")
                print(f"📊 Taille: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Fichier EXE non trouvé")
                return False
        else:
            print(f"❌ Erreur PyInstaller:")
            print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout - Le build a pris trop de temps")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_portable_package():
    """Créer un package portable"""
    print("\n📦 Création du package portable...")
    
    try:
        # Créer le dossier de distribution
        dist_folder = Path("INVESTT_Portable")
        if dist_folder.exists():
            shutil.rmtree(dist_folder)
        
        dist_folder.mkdir()
        
        # Copier l'EXE
        exe_source = Path("dist/INVESTT_Trading_Bot.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_folder / "INVESTT_Trading_Bot.exe")
        
        # Créer un fichier .env par défaut
        env_content = '''# INVESTT Trading Bot Configuration
BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=true

# Trading Parameters
INITIAL_CAPITAL=1000.0
MAX_POSITION_SIZE=0.02
MAX_DAILY_LOSS=150.0
MAX_DRAWDOWN=0.10

# Strategy Parameters
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0

# Mode - CHANGEZ ICI POUR LIVE TRADING
PAPER_TRADING=true
LIVE_TRADING=false

# Logging
LOG_LEVEL=INFO
'''
        
        with open(dist_folder / ".env", "w") as f:
            f.write(env_content)
        
        # Créer un fichier de démarrage rapide
        quick_start = '''@echo off
title INVESTT Trading Bot
echo.
echo ========================================
echo    🚀 INVESTT TRADING BOT
echo ========================================
echo.
echo Lancement de l'application...
echo.

start "" "INVESTT_Trading_Bot.exe"

echo ✅ Application lancée!
echo.
echo Fermez cette fenêtre si l'application s'ouvre correctement.
echo.
pause
'''
        
        with open(dist_folder / "Lancer_INVESTT.bat", "w") as f:
            f.write(quick_start)
        
        # Créer un README pour l'utilisateur
        user_readme = '''# 🚀 INVESTT Trading Bot - Version Portable

## 🎯 Démarrage Rapide

1. **Double-cliquez** sur `INVESTT_Trading_Bot.exe`
2. **OU** double-cliquez sur `Lancer_INVESTT.bat`

## ⚙️ Configuration pour Live Trading

1. **Éditez** le fichier `.env` avec vos clés API Binance
2. **Changez** `LIVE_TRADING=true` et `PAPER_TRADING=false`
3. **Relancez** l'application
4. **Basculez** le switch "LIVE TRADING" dans l'interface

## 🛡️ Sécurité

- ⚠️ Commencez TOUJOURS en Paper Trading
- ⚠️ Testez d'abord sur Binance Testnet
- ⚠️ Ne risquez jamais plus que vous pouvez perdre
- ⚠️ Surveillez les performances régulièrement

## 🔧 Obtenir les Clés API Binance

1. Créez un compte sur binance.com
2. Allez dans Profil > Sécurité API
3. Créez une nouvelle API avec permissions:
   - ✅ Lecture des informations
   - ✅ Trading au comptant
   - ❌ Retrait (JAMAIS!)
4. Copiez les clés dans le fichier .env

## 📊 Interface

L'application dispose d'une interface moderne avec:
- 💰 Switch Live/Paper Trading
- 🚀 Boutons Start/Stop/Pause
- 📊 Métriques en temps réel
- 📝 Logs d'activité
- ⚙️ Configuration des paramètres

Bon trading ! 🚀💰
'''
        
        with open(dist_folder / "LISEZMOI.txt", "w", encoding='utf-8') as f:
            f.write(user_readme)
        
        print(f"✅ Package portable créé: {dist_folder}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création package: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CRÉATION D'UN EXE PORTABLE INVESTT (VERSION CORRIGÉE)")
    print("=" * 60)
    
    # Vérifier que le fichier principal existe
    if not Path("investt_app_fixed.py").exists():
        print("❌ Fichier investt_app_fixed.py non trouvé!")
        print("💡 Assurez-vous d'être dans le bon dossier")
        input("Appuyez sur Entrée pour fermer...")
        return
    
    # Étapes
    steps = [
        ("Installation dépendances", install_dependencies),
        ("Création icône", create_simple_icon),
        ("Création fichier .spec", create_spec_file),
        ("Construction EXE", build_exe),
        ("Création package portable", create_portable_package)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_func in [build_exe, create_portable_package]:
                success = step_func()
                if success:
                    success_count += 1
            else:
                step_func()
                success_count += 1
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    # Résumé
    print("\n" + "=" * 60)
    print("🎉 PROCESSUS TERMINÉ!")
    print("=" * 60)
    
    if Path("INVESTT_Portable").exists() and Path("INVESTT_Portable/INVESTT_Trading_Bot.exe").exists():
        print("✅ EXE PORTABLE CRÉÉ AVEC SUCCÈS!")
        print("\n📁 Dossier: INVESTT_Portable/")
        print("🚀 Fichier: INVESTT_Trading_Bot.exe")
        print(f"📊 Taille: ~{Path('INVESTT_Portable/INVESTT_Trading_Bot.exe').stat().st_size / (1024*1024):.1f} MB")
        print("\n🎯 Instructions:")
        print("   1. Allez dans le dossier 'INVESTT_Portable'")
        print("   2. Double-cliquez sur 'INVESTT_Trading_Bot.exe'")
        print("   3. L'interface moderne s'ouvre!")
        print("   4. Pour live trading: éditez .env avec vos clés API")
        print("   5. Basculez le switch 'LIVE TRADING' dans l'app")
        print("\n💡 Vous pouvez copier ce dossier n'importe où!")
        print("💡 Aucune installation Python requise sur d'autres PC!")
    else:
        print("⚠️ EXE non créé complètement")
        print(f"✅ Étapes réussies: {success_count}/{len(steps)}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Processus interrompu")
    except Exception as e:
        print(f"\n\n❌ Erreur critique: {e}")
    finally:
        input("\nAppuyez sur Entrée pour fermer...")
