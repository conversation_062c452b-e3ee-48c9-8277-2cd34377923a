# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['investt_app_fixed.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config_simple.py', '.'),
        ('.env.example', '.'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'customtkinter',
        'tkinter',
        'PIL',
        'dotenv',
        'threading',
        'pathlib'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'pandas',
        'numpy', 
        'ccxt',
        'sqlalchemy',
        'loguru',
        'plotly',
        'websocket',
        'pydantic',
        'matplotlib',
        'seaborn'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='INVESTT_Trading_Bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
