# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['investt_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.py', '.'),
        ('src', 'src'),
        ('.env.example', '.'),
        ('README.md', '.'),
        ('LIVE_TRADING_GUIDE.md', '.'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'customtkinter',
        'pandas',
        'numpy', 
        'ccxt',
        'sqlalchemy',
        'loguru',
        'plotly',
        'PIL',
        'websocket',
        'dotenv',
        'threading',
        'tkinter',
        'pandas_ta',
        'pydantic'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='INVESTT_Trading_Bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Pas de console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
