"""
🚀 INVESTT Trading Bot - Créateur d'EXE Simple
Crée un fichier .exe portable avec PyInstaller
"""
import subprocess
import sys
import os
from pathlib import Path
import shutil

def install_pyinstaller():
    """Installer PyInstaller"""
    print("📦 Installation de PyInstaller...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "pip", "install", "pyinstaller"],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ PyInstaller installé")
            return True
        else:
            print(f"❌ Erreur: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_simple_icon():
    """Créer une icône simple"""
    print("🎨 Création de l'icône...")
    
    try:
        from PIL import Image, ImageDraw
        
        # Créer une icône simple
        size = 256
        img = Image.new('RGBA', (size, size), (30, 144, 255, 255))  # Bleu
        draw = ImageDraw.Draw(img)
        
        # Cercle blanc
        margin = 30
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(255, 255, 255, 255), outline=(0, 0, 0, 255), width=4)
        
        # Texte "I"
        from PIL import ImageFont
        try:
            font = ImageFont.truetype("arial.ttf", 100)
        except:
            font = ImageFont.load_default()
        
        text = "I"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 10
        
        draw.text((x, y), text, fill=(30, 144, 255, 255), font=font)
        
        # Sauvegarder
        os.makedirs("assets", exist_ok=True)
        img.save("assets/icon.ico", format='ICO', sizes=[(256, 256), (64, 64), (32, 32), (16, 16)])
        print("✅ Icône créée")
        return True
        
    except ImportError:
        print("⚠️ PIL non disponible, icône non créée")
        return False
    except Exception as e:
        print(f"⚠️ Erreur création icône: {e}")
        return False

def create_spec_file():
    """Créer un fichier .spec pour PyInstaller"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['investt_app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('config.py', '.'),
        ('src', 'src'),
        ('.env.example', '.'),
        ('README.md', '.'),
        ('LIVE_TRADING_GUIDE.md', '.'),
        ('assets', 'assets'),
    ],
    hiddenimports=[
        'customtkinter',
        'pandas',
        'numpy', 
        'ccxt',
        'sqlalchemy',
        'loguru',
        'plotly',
        'PIL',
        'websocket',
        'dotenv',
        'threading',
        'tkinter',
        'pandas_ta',
        'pydantic'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='INVESTT_Trading_Bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # Pas de console
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='assets/icon.ico' if os.path.exists('assets/icon.ico') else None,
)
'''
    
    with open("investt.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ Fichier .spec créé")

def build_exe():
    """Construire l'EXE avec PyInstaller"""
    print("\n🔨 Construction de l'EXE...")
    print("⏳ Cela peut prendre plusieurs minutes...")
    
    try:
        # Utiliser le fichier .spec
        result = subprocess.run(
            [sys.executable, "-m", "PyInstaller", "--clean", "investt.spec"],
            capture_output=True,
            text=True,
            timeout=600  # 10 minutes max
        )
        
        if result.returncode == 0:
            print("✅ EXE créé avec succès!")
            
            # Vérifier si le fichier existe
            exe_path = Path("dist/INVESTT_Trading_Bot.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Fichier: {exe_path}")
                print(f"📊 Taille: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Fichier EXE non trouvé")
                return False
        else:
            print(f"❌ Erreur PyInstaller:")
            print(result.stderr[:500] + "..." if len(result.stderr) > 500 else result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout - Le build a pris trop de temps")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_portable_package():
    """Créer un package portable"""
    print("\n📦 Création du package portable...")
    
    try:
        # Créer le dossier de distribution
        dist_folder = Path("INVESTT_Portable")
        if dist_folder.exists():
            shutil.rmtree(dist_folder)
        
        dist_folder.mkdir()
        
        # Copier l'EXE
        exe_source = Path("dist/INVESTT_Trading_Bot.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, dist_folder / "INVESTT_Trading_Bot.exe")
        
        # Copier les fichiers essentiels
        files_to_copy = [
            ".env.example",
            "README.md", 
            "LIVE_TRADING_GUIDE.md"
        ]
        
        for file in files_to_copy:
            if Path(file).exists():
                shutil.copy2(file, dist_folder / file)
        
        # Créer un fichier de démarrage rapide
        quick_start = '''@echo off
title INVESTT Trading Bot
echo.
echo ========================================
echo    🚀 INVESTT TRADING BOT
echo ========================================
echo.
echo Lancement de l'application...
echo.

start "" "INVESTT_Trading_Bot.exe"

echo ✅ Application lancée!
echo.
echo Fermez cette fenêtre si l'application s'ouvre correctement.
echo.
pause
'''
        
        with open(dist_folder / "Lancer_INVESTT.bat", "w") as f:
            f.write(quick_start)
        
        # Créer un README pour l'utilisateur
        user_readme = '''# 🚀 INVESTT Trading Bot - Version Portable

## 🎯 Démarrage Rapide

1. **Double-cliquez** sur `INVESTT_Trading_Bot.exe`
2. **OU** double-cliquez sur `Lancer_INVESTT.bat`

## ⚙️ Configuration

1. **Copiez** `.env.example` vers `.env`
2. **Éditez** `.env` avec vos clés API Binance
3. **Relancez** l'application

## 📚 Documentation

- `README.md` - Guide complet
- `LIVE_TRADING_GUIDE.md` - Guide live trading

## ⚠️ Important

- Commencez TOUJOURS en Paper Trading
- Ne risquez jamais plus que vous pouvez perdre
- Surveillez les performances régulièrement

Bon trading ! 🚀💰
'''
        
        with open(dist_folder / "LISEZMOI.txt", "w", encoding='utf-8') as f:
            f.write(user_readme)
        
        print(f"✅ Package portable créé: {dist_folder}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création package: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CRÉATION D'UN EXE PORTABLE INVESTT")
    print("=" * 50)
    
    # Vérifier que le fichier principal existe
    if not Path("investt_app.py").exists():
        print("❌ Fichier investt_app.py non trouvé!")
        print("💡 Assurez-vous d'être dans le bon dossier")
        input("Appuyez sur Entrée pour fermer...")
        return
    
    # Étapes
    steps = [
        ("Installation PyInstaller", install_pyinstaller),
        ("Création icône", create_simple_icon),
        ("Création fichier .spec", create_spec_file),
        ("Construction EXE", build_exe),
        ("Création package portable", create_portable_package)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            if step_func in [install_pyinstaller, build_exe, create_portable_package]:
                success = step_func()
                if success:
                    success_count += 1
            else:
                step_func()
                success_count += 1
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    # Résumé
    print("\n" + "=" * 50)
    print("🎉 PROCESSUS TERMINÉ!")
    print("=" * 50)
    
    if Path("INVESTT_Portable").exists() and Path("INVESTT_Portable/INVESTT_Trading_Bot.exe").exists():
        print("✅ EXE PORTABLE CRÉÉ AVEC SUCCÈS!")
        print("\n📁 Dossier: INVESTT_Portable/")
        print("🚀 Fichier: INVESTT_Trading_Bot.exe")
        print("\n🎯 Instructions:")
        print("   1. Allez dans le dossier 'INVESTT_Portable'")
        print("   2. Double-cliquez sur 'INVESTT_Trading_Bot.exe'")
        print("   3. OU double-cliquez sur 'Lancer_INVESTT.bat'")
        print("   4. Configurez vos clés API dans .env")
        print("   5. Commencez à trader!")
        print("\n💡 Vous pouvez copier ce dossier n'importe où!")
    else:
        print("⚠️ EXE non créé complètement")
        print(f"✅ Étapes réussies: {success_count}/{len(steps)}")
        
        if Path("dist/INVESTT_Trading_Bot.exe").exists():
            print("\n💡 L'EXE existe dans 'dist/', vous pouvez l'utiliser directement")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Processus interrompu")
    except Exception as e:
        print(f"\n\n❌ Erreur critique: {e}")
    finally:
        input("\nAppuyez sur Entrée pour fermer...")
