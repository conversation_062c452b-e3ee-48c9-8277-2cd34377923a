"""
🤖👹 INVESTT MONSTER BOT - Next Gen Trading Beast
DEX Trading + Design de Malade + Scalping Ultra-Rapide
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import threading
import time
import random
import math
from datetime import datetime
import json

# Configuration MONSTER
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("green")  # Thème MONSTER

# Configuration MONSTER
class MonsterConfig:
    def __init__(self):
        # Trading MONSTER
        self.INITIAL_CAPITAL = 1000.0
        self.MAX_POSITION_SIZE = 0.05  # 5% par trade (plus agressif)
        self.SCALP_MODE = True
        self.SHITCOIN_MODE = True
        
        # DEX Configuration
        self.DEX_ENABLED = True
        self.WALLET_ADDRESS = ""
        self.PRIVATE_KEY = ""  # Chiffré
        self.SLIPPAGE = 0.5  # 0.5% slippage
        
        # MONSTER Pairs (Shitcoins + Majors)
        self.MONSTER_PAIRS = [
            # Shitcoins populaires
            "PEPE/ETH", "SHIB/ETH", "DOGE/ETH", "FLOKI/ETH",
            "BONK/SOL", "WIF/SOL", "BOME/SOL", "MYRO/SOL",
            # Majors pour stabilité
            "ETH/USDT", "BTC/USDT", "SOL/USDT", "BNB/USDT"
        ]
        
        # Scalping MONSTER
        self.SCALP_TIMEFRAME = "1s"  # 1 seconde !
        self.MIN_PROFIT = 0.002  # 0.2% minimum
        self.MAX_HOLD_TIME = 30  # 30 secondes max
        self.PUMP_THRESHOLD = 0.05  # 5% pump detection

config = MonsterConfig()

# IA MONSTER - Beast Mode
class MonsterAI:
    """IA MONSTER pour scalping ultra-rapide"""
    
    def __init__(self):
        # Métriques MONSTER
        self.confidence = 0.85
        self.scalp_accuracy = 0.75
        self.pump_detection_rate = 0.80
        self.shitcoin_score = 0.0
        
        # MONSTER Stats
        self.total_scalps = 0
        self.successful_scalps = 0
        self.total_volume = 0.0
        self.biggest_pump = 0.0
        
        # MONSTER Modes
        self.beast_mode = False
        self.pump_hunter = True
        self.shitcoin_hunter = True
        self.lightning_scalp = True
        
        # MONSTER Patterns
        self.pump_patterns = []
        self.dump_patterns = []
        self.volume_spikes = []
        
        # MONSTER Learning
        self.successful_patterns = {}
        self.failed_patterns = {}
    
    def detect_pump(self, symbol, price_data):
        """Détecteur de PUMP ultra-rapide"""
        try:
            if len(price_data) < 10:
                return False, 0.0
            
            # Calcul du pump récent
            recent_change = (price_data[-1] - price_data[-10]) / price_data[-10]
            
            # Volume spike detection
            volume_spike = random.uniform(0.5, 3.0)  # Simulation
            
            # Pattern recognition
            is_pumping = recent_change > config.PUMP_THRESHOLD
            
            # MONSTER Score
            pump_score = 0.0
            if is_pumping:
                pump_score = min(recent_change * 10, 1.0)
                
                # Bonus pour shitcoins
                if any(shit in symbol for shit in ["PEPE", "SHIB", "DOGE", "FLOKI", "BONK"]):
                    pump_score *= 1.5
                
                # Bonus pour volume
                if volume_spike > 2.0:
                    pump_score *= 1.3
            
            self.biggest_pump = max(self.biggest_pump, recent_change)
            
            return is_pumping, pump_score
            
        except Exception as e:
            print(f"Erreur détection pump: {e}")
            return False, 0.0
    
    def scalp_analysis(self, symbol):
        """Analyse MONSTER pour scalping"""
        try:
            # Générer des données ultra-rapides
            base_price = random.uniform(0.000001, 100.0)  # Shitcoin range
            prices = []
            volumes = []
            
            # Simulation de données 1 seconde
            for i in range(60):  # 60 points = 1 minute de données
                if i == 0:
                    prices.append(base_price)
                    volumes.append(random.uniform(1000, 10000))
                else:
                    # Volatilité MONSTER pour shitcoins
                    if any(shit in symbol for shit in ["PEPE", "SHIB", "BONK", "FLOKI"]):
                        change = random.uniform(-0.1, 0.1)  # ±10% par seconde !
                    else:
                        change = random.uniform(-0.02, 0.02)  # ±2% pour majors
                    
                    new_price = prices[-1] * (1 + change)
                    prices.append(new_price)
                    volumes.append(random.uniform(500, 15000))
            
            # Détection PUMP
            is_pump, pump_score = self.detect_pump(symbol, prices)
            
            # Micro-tendance (1 seconde)
            micro_trend = (prices[-1] - prices[-5]) / prices[-5] if len(prices) >= 5 else 0
            
            # Volume analysis
            recent_volume = sum(volumes[-10:]) / 10
            avg_volume = sum(volumes) / len(volumes)
            volume_ratio = recent_volume / avg_volume if avg_volume > 0 else 1
            
            # Volatilité MONSTER
            price_changes = [abs(prices[i] - prices[i-1])/prices[i-1] for i in range(1, len(prices))]
            volatility = sum(price_changes[-10:]) / 10 if price_changes else 0
            
            # Score SHITCOIN
            shitcoin_multiplier = 1.0
            if any(shit in symbol for shit in ["PEPE", "SHIB", "DOGE", "FLOKI", "BONK", "WIF", "BOME"]):
                shitcoin_multiplier = 2.0
                self.shitcoin_score = min(1.0, volatility * 10)
            
            # Signal SCALP
            scalp_signal = 0.0
            
            # PUMP Signal (priorité absolue)
            if is_pump and pump_score > 0.3:
                scalp_signal = pump_score * shitcoin_multiplier
            
            # Micro-trend signal
            elif abs(micro_trend) > 0.01:  # 1% micro-mouvement
                scalp_signal = micro_trend * 2 * shitcoin_multiplier
            
            # Volume spike signal
            elif volume_ratio > 1.5:
                scalp_signal = (volume_ratio - 1) * 0.5 * shitcoin_multiplier
            
            # Volatilité signal
            elif volatility > 0.02:  # 2% volatilité
                scalp_signal = volatility * 5 * shitcoin_multiplier
            
            # Confiance MONSTER
            confidence_factors = [
                pump_score,
                min(volatility * 10, 1.0),
                min(volume_ratio / 3, 1.0),
                shitcoin_multiplier / 2
            ]
            
            self.confidence = sum(confidence_factors) / len(confidence_factors)
            
            # Décision SCALP
            if abs(scalp_signal) > 0.15 and self.confidence > 0.6:
                action = "BUY" if scalp_signal > 0 else "SELL"
                
                # Calcul du hold time (plus c'est volatile, moins on hold)
                hold_time = max(1, config.MAX_HOLD_TIME - (volatility * 100))
                
                return {
                    'action': action,
                    'strength': min(abs(scalp_signal), 1.0),
                    'confidence': self.confidence,
                    'pump_detected': is_pump,
                    'pump_score': pump_score,
                    'volatility': volatility,
                    'volume_ratio': volume_ratio,
                    'shitcoin_score': self.shitcoin_score,
                    'hold_time': hold_time,
                    'entry_price': prices[-1],
                    'target_profit': config.MIN_PROFIT * (1 + volatility * 5),
                    'analysis': {
                        'micro_trend': micro_trend,
                        'volume_spike': volume_ratio > 1.5,
                        'is_shitcoin': shitcoin_multiplier > 1,
                        'beast_mode_ready': self.confidence > 0.8
                    }
                }
            
            return None
            
        except Exception as e:
            print(f"Erreur analyse MONSTER: {e}")
            return None
    
    def learn_from_scalp(self, scalp_result):
        """Apprentissage MONSTER"""
        try:
            self.total_scalps += 1
            
            if scalp_result.get('profitable', False):
                self.successful_scalps += 1
            
            # Calcul précision
            if self.total_scalps > 0:
                self.scalp_accuracy = self.successful_scalps / self.total_scalps
            
            # Mode BEAST activation
            if self.scalp_accuracy > 0.8 and self.total_scalps > 20:
                self.beast_mode = True
            elif self.scalp_accuracy < 0.6:
                self.beast_mode = False
            
            # Adaptation des seuils
            if self.scalp_accuracy > 0.75:
                config.MIN_PROFIT = max(0.001, config.MIN_PROFIT - 0.0001)  # Plus agressif
            elif self.scalp_accuracy < 0.5:
                config.MIN_PROFIT = min(0.005, config.MIN_PROFIT + 0.0001)  # Plus conservateur
            
        except Exception as e:
            print(f"Erreur apprentissage MONSTER: {e}")
    
    def get_monster_status(self):
        """Status MONSTER"""
        try:
            return {
                'confidence': self.confidence,
                'scalp_accuracy': self.scalp_accuracy,
                'pump_detection_rate': self.pump_detection_rate,
                'shitcoin_score': self.shitcoin_score,
                'total_scalps': self.total_scalps,
                'successful_scalps': self.successful_scalps,
                'win_rate': self.scalp_accuracy,
                'biggest_pump': self.biggest_pump,
                'beast_mode': self.beast_mode,
                'modes': {
                    'pump_hunter': self.pump_hunter,
                    'shitcoin_hunter': self.shitcoin_hunter,
                    'lightning_scalp': self.lightning_scalp
                }
            }
        except:
            return {
                'confidence': 0.85,
                'scalp_accuracy': 0.75,
                'pump_detection_rate': 0.80,
                'shitcoin_score': 0.0,
                'total_scalps': 0,
                'successful_scalps': 0,
                'win_rate': 0.0,
                'biggest_pump': 0.0,
                'beast_mode': False,
                'modes': {
                    'pump_hunter': True,
                    'shitcoin_hunter': True,
                    'lightning_scalp': True
                }
            }

# Instance MONSTER
monster_ai = MonsterAI()


class MonsterTradingApp:
    """MONSTER Trading App - Design de MALADE"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.setup_monster_window()
        self.setup_monster_variables()
        self.create_monster_widgets()
        self.start_monster_animations()
        
        # État MONSTER
        self.is_running = False
        self.beast_mode_active = False
        self.scalps_count = 0
        self.successful_scalps = 0
        self.portfolio_value_num = config.INITIAL_CAPITAL
        self.daily_pnl_num = 0.0
        self.total_volume_traded = 0.0
    
    def setup_monster_window(self):
        """Configuration MONSTER Window"""
        self.root.title("🤖👹 INVESTT MONSTER BOT - Beast Mode Trading")
        self.root.geometry("1800x1200")
        self.root.minsize(1600, 1000)
        
        # Style MONSTER
        self.root.configure(fg_color=("#0a0a0a", "#0a0a0a"))  # Noir profond
        
        # Centrer
        x = (self.root.winfo_screenwidth() // 2) - (1800 // 2)
        y = (self.root.winfo_screenheight() // 2) - (1200 // 2)
        self.root.geometry(f"1800x1200+{x}+{y}")
    
    def setup_monster_variables(self):
        """Variables MONSTER"""
        # Trading
        self.dex_mode = tk.BooleanVar(value=True)
        self.beast_mode = tk.BooleanVar(value=False)
        self.pump_hunter = tk.BooleanVar(value=True)
        self.shitcoin_hunter = tk.BooleanVar(value=True)
        self.lightning_scalp = tk.BooleanVar(value=True)
        
        # Métriques principales
        self.portfolio_value = tk.StringVar(value=f"${config.INITIAL_CAPITAL:,.2f}")
        self.daily_pnl = tk.StringVar(value="$0.00")
        self.total_scalps = tk.StringVar(value="0")
        self.scalp_accuracy = tk.StringVar(value="0%")
        self.status_text = tk.StringVar(value="🔴 MONSTER SLEEPING")
        
        # Métriques MONSTER
        self.monster_confidence = tk.StringVar(value="85%")
        self.pump_detection = tk.StringVar(value="80%")
        self.shitcoin_score = tk.StringVar(value="0%")
        self.biggest_pump = tk.StringVar(value="0%")
        self.volume_traded = tk.StringVar(value="$0")
        
        # Configuration MONSTER
        self.wallet_address = tk.StringVar(value="")
        self.slippage = tk.StringVar(value="0.5")
        self.min_profit = tk.StringVar(value="0.2")
        self.max_hold_time = tk.StringVar(value="30")
    
    def create_monster_widgets(self):
        """Interface MONSTER - Design de MALADE"""
        
        # === HEADER MONSTER ===
        header_frame = ctk.CTkFrame(
            self.root, 
            height=100, 
            corner_radius=0,
            fg_color=("#1a1a1a", "#1a1a1a"),
            border_width=2,
            border_color=("#00ff00", "#00ff00")  # Bordure verte MONSTER
        )
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # Titre MONSTER avec effet
        title_label = ctk.CTkLabel(
            header_frame, 
            text="🤖👹 INVESTT MONSTER BOT - BEAST MODE TRADING", 
            font=ctk.CTkFont(size=28, weight="bold"),
            text_color=("#00ff00", "#00ff00")  # Vert MONSTER
        )
        title_label.pack(side="left", padx=20, pady=30)
        
        # Status MONSTER avec animation
        status_frame = ctk.CTkFrame(header_frame, fg_color="transparent")
        status_frame.pack(side="right", padx=20, pady=20)
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            textvariable=self.status_text,
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color=("#ff0000", "#ff0000")  # Rouge par défaut
        )
        self.status_label.pack()
        
        # Beast Mode Indicator
        self.beast_indicator = ctk.CTkLabel(
            status_frame,
            text="😴 SLEEPING",
            font=ctk.CTkFont(size=14),
            text_color=("#666666", "#666666")
        )
        self.beast_indicator.pack()
        
        # === MAIN CONTAINER MONSTER ===
        main_container = ctk.CTkFrame(
            self.root, 
            corner_radius=10,
            fg_color=("#111111", "#111111"),
            border_width=1,
            border_color=("#333333", "#333333")
        )
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # === LEFT PANEL - MONSTER CONTROLS ===
        left_panel = ctk.CTkFrame(
            main_container, 
            width=450,
            fg_color=("#1a1a1a", "#1a1a1a"),
            border_width=1,
            border_color=("#00ff00", "#00ff00")
        )
        left_panel.pack(side="left", fill="y", padx=(10, 5), pady=10)
        left_panel.pack_propagate(False)
        
        self.create_monster_control_panel(left_panel)
        
        # === CENTER PANEL - MONSTER MONITORING ===
        center_panel = ctk.CTkFrame(
            main_container,
            fg_color=("#1a1a1a", "#1a1a1a"),
            border_width=1,
            border_color=("#ffff00", "#ffff00")  # Bordure jaune
        )
        center_panel.pack(side="left", fill="both", expand=True, padx=5, pady=10)
        
        self.create_monster_monitoring_panel(center_panel)
        
        # === RIGHT PANEL - MONSTER AI ===
        right_panel = ctk.CTkFrame(
            main_container, 
            width=450,
            fg_color=("#1a1a1a", "#1a1a1a"),
            border_width=1,
            border_color=("#ff00ff", "#ff00ff")  # Bordure magenta
        )
        right_panel.pack(side="right", fill="y", padx=(5, 10), pady=10)
        right_panel.pack_propagate(False)
        
        self.create_monster_ai_panel(right_panel)

    def create_monster_control_panel(self, parent):
        """Panneau de contrôle MONSTER"""

        # Titre MONSTER
        title_frame = ctk.CTkFrame(parent, fg_color="transparent")
        title_frame.pack(fill="x", padx=15, pady=(15, 10))

        ctk.CTkLabel(
            title_frame,
            text="⚙️ MONSTER CONTROLS",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("#00ff00", "#00ff00")
        ).pack()

        # === DEX MODE ===
        dex_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#00ff00", "#00ff00")
        )
        dex_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            dex_frame,
            text="🔗 DEX Trading Mode",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#00ff00", "#00ff00")
        ).pack(pady=(15, 5))

        dex_switch = ctk.CTkSwitch(
            dex_frame,
            text="DEX ENABLED",
            variable=self.dex_mode,
            command=self.toggle_dex_mode,
            font=ctk.CTkFont(size=14, weight="bold"),
            progress_color=("#00ff00", "#00ff00"),
            button_color=("#004400", "#004400")
        )
        dex_switch.pack(pady=10)

        self.dex_warning = ctk.CTkLabel(
            dex_frame,
            text="🚀 NO KYC - PURE FREEDOM!",
            text_color=("#00ff00", "#00ff00"),
            font=ctk.CTkFont(size=12, weight="bold")
        )
        self.dex_warning.pack(pady=(0, 15))

        # === MONSTER MODES ===
        modes_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#ff6600", "#ff6600")
        )
        modes_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            modes_frame,
            text="👹 MONSTER MODES",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ff6600", "#ff6600")
        ).pack(pady=(15, 10))

        # Beast Mode
        beast_switch = ctk.CTkSwitch(
            modes_frame,
            text="🔥 BEAST MODE",
            variable=self.beast_mode,
            command=self.toggle_beast_mode,
            font=ctk.CTkFont(size=12, weight="bold"),
            progress_color=("#ff0000", "#ff0000"),
            button_color=("#440000", "#440000")
        )
        beast_switch.pack(pady=5)

        # Pump Hunter
        pump_switch = ctk.CTkSwitch(
            modes_frame,
            text="🚀 PUMP HUNTER",
            variable=self.pump_hunter,
            font=ctk.CTkFont(size=12),
            progress_color=("#ffff00", "#ffff00"),
            button_color=("#444400", "#444400")
        )
        pump_switch.pack(pady=5)

        # Shitcoin Hunter
        shit_switch = ctk.CTkSwitch(
            modes_frame,
            text="💩 SHITCOIN HUNTER",
            variable=self.shitcoin_hunter,
            font=ctk.CTkFont(size=12),
            progress_color=("#ff00ff", "#ff00ff"),
            button_color=("#440044", "#440044")
        )
        shit_switch.pack(pady=5)

        # Lightning Scalp
        scalp_switch = ctk.CTkSwitch(
            modes_frame,
            text="⚡ LIGHTNING SCALP",
            variable=self.lightning_scalp,
            font=ctk.CTkFont(size=12),
            progress_color=("#00ffff", "#00ffff"),
            button_color=("#004444", "#004444")
        )
        scalp_switch.pack(pady=(5, 15))

        # === BOUTONS MONSTER ===
        buttons_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#ff0000", "#ff0000")
        )
        buttons_frame.pack(fill="x", padx=15, pady=15)

        self.start_button = ctk.CTkButton(
            buttons_frame,
            text="🚀 UNLEASH THE MONSTER",
            command=self.toggle_monster,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            fg_color=("#006600", "#006600"),
            hover_color=("#008800", "#008800"),
            text_color=("#ffffff", "#ffffff")
        )
        self.start_button.pack(fill="x", padx=10, pady=10)

        self.emergency_button = ctk.CTkButton(
            buttons_frame,
            text="🛑 EMERGENCY STOP",
            command=self.emergency_stop,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            fg_color=("#cc0000", "#cc0000"),
            hover_color=("#ff0000", "#ff0000")
        )
        self.emergency_button.pack(fill="x", padx=10, pady=(0, 10))

        # === CONFIGURATION MONSTER ===
        config_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#6666ff", "#6666ff")
        )
        config_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            config_frame,
            text="⚙️ MONSTER CONFIG",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#6666ff", "#6666ff")
        ).pack(pady=(15, 10))

        # Wallet Address
        ctk.CTkLabel(config_frame, text="🔑 Wallet Address", font=ctk.CTkFont(size=12), text_color=("#cccccc", "#cccccc")).pack(anchor="w", padx=10)
        wallet_entry = ctk.CTkEntry(
            config_frame,
            textvariable=self.wallet_address,
            height=35,
            placeholder_text="0x...",
            font=ctk.CTkFont(size=10)
        )
        wallet_entry.pack(fill="x", padx=10, pady=2)

        # Slippage
        ctk.CTkLabel(config_frame, text="💧 Slippage (%)", font=ctk.CTkFont(size=12), text_color=("#cccccc", "#cccccc")).pack(anchor="w", padx=10, pady=(5,0))
        slippage_entry = ctk.CTkEntry(config_frame, textvariable=self.slippage, height=30)
        slippage_entry.pack(fill="x", padx=10, pady=2)

        # Min Profit
        ctk.CTkLabel(config_frame, text="💰 Min Profit (%)", font=ctk.CTkFont(size=12), text_color=("#cccccc", "#cccccc")).pack(anchor="w", padx=10, pady=(5,0))
        profit_entry = ctk.CTkEntry(config_frame, textvariable=self.min_profit, height=30)
        profit_entry.pack(fill="x", padx=10, pady=2)

        # Max Hold Time
        ctk.CTkLabel(config_frame, text="⏱️ Max Hold (sec)", font=ctk.CTkFont(size=12), text_color=("#cccccc", "#cccccc")).pack(anchor="w", padx=10, pady=(5,0))
        hold_entry = ctk.CTkEntry(config_frame, textvariable=self.max_hold_time, height=30)
        hold_entry.pack(fill="x", padx=10, pady=(2, 15))

    def create_monster_monitoring_panel(self, parent):
        """Panneau de monitoring MONSTER"""

        # Titre
        title_frame = ctk.CTkFrame(parent, fg_color="transparent")
        title_frame.pack(fill="x", padx=15, pady=(15, 10))

        ctk.CTkLabel(
            title_frame,
            text="📊 MONSTER MONITORING",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("#ffff00", "#ffff00")
        ).pack()

        # === MÉTRIQUES MONSTER ===
        metrics_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#ffff00", "#ffff00")
        )
        metrics_frame.pack(fill="x", padx=15, pady=10)

        # Grille 2x3 MONSTER
        metrics_grid = ctk.CTkFrame(metrics_frame, fg_color="transparent")
        metrics_grid.pack(fill="x", padx=10, pady=15)

        # Portfolio
        portfolio_frame = ctk.CTkFrame(
            metrics_grid,
            fg_color=("#003300", "#003300"),
            border_width=1,
            border_color=("#00ff00", "#00ff00")
        )
        portfolio_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(portfolio_frame, text="💼 Portfolio", font=ctk.CTkFont(size=11), text_color=("#00ff00", "#00ff00")).pack(pady=(5,0))
        self.portfolio_label = ctk.CTkLabel(
            portfolio_frame,
            textvariable=self.portfolio_value,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#00ff00", "#00ff00")
        )
        self.portfolio_label.pack(pady=(0,5))

        # P&L
        pnl_frame = ctk.CTkFrame(
            metrics_grid,
            fg_color=("#330033", "#330033"),
            border_width=1,
            border_color=("#ff00ff", "#ff00ff")
        )
        pnl_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(pnl_frame, text="📈 P&L Monster", font=ctk.CTkFont(size=11), text_color=("#ff00ff", "#ff00ff")).pack(pady=(5,0))
        self.pnl_label = ctk.CTkLabel(
            pnl_frame,
            textvariable=self.daily_pnl,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ff00ff", "#ff00ff")
        )
        self.pnl_label.pack(pady=(0,5))

        # Scalps
        scalps_frame = ctk.CTkFrame(
            metrics_grid,
            fg_color=("#333300", "#333300"),
            border_width=1,
            border_color=("#ffff00", "#ffff00")
        )
        scalps_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(scalps_frame, text="⚡ Scalps", font=ctk.CTkFont(size=11), text_color=("#ffff00", "#ffff00")).pack(pady=(5,0))
        ctk.CTkLabel(
            scalps_frame,
            textvariable=self.total_scalps,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ffff00", "#ffff00")
        ).pack(pady=(0,5))

        # Accuracy
        accuracy_frame = ctk.CTkFrame(
            metrics_grid,
            fg_color=("#003333", "#003333"),
            border_width=1,
            border_color=("#00ffff", "#00ffff")
        )
        accuracy_frame.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(accuracy_frame, text="🎯 Accuracy", font=ctk.CTkFont(size=11), text_color=("#00ffff", "#00ffff")).pack(pady=(5,0))
        ctk.CTkLabel(
            accuracy_frame,
            textvariable=self.scalp_accuracy,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#00ffff", "#00ffff")
        ).pack(pady=(0,5))

        # Volume
        volume_frame = ctk.CTkFrame(
            metrics_grid,
            fg_color=("#330000", "#330000"),
            border_width=1,
            border_color=("#ff0000", "#ff0000")
        )
        volume_frame.grid(row=2, column=0, columnspan=2, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(volume_frame, text="📊 Volume Traded", font=ctk.CTkFont(size=11), text_color=("#ff0000", "#ff0000")).pack(pady=(5,0))
        ctk.CTkLabel(
            volume_frame,
            textvariable=self.volume_traded,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ff0000", "#ff0000")
        ).pack(pady=(0,5))

        metrics_grid.grid_columnconfigure(0, weight=1)
        metrics_grid.grid_columnconfigure(1, weight=1)

        # === LOGS MONSTER ===
        log_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#ffffff", "#ffffff")
        )
        log_frame.pack(fill="both", expand=True, padx=15, pady=10)

        ctk.CTkLabel(
            log_frame,
            text="📝 MONSTER LOGS - REAL TIME",
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ffffff", "#ffffff")
        ).pack(pady=(15, 10))

        self.log_text = ctk.CTkTextbox(
            log_frame,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word",
            fg_color=("#000000", "#000000"),
            text_color=("#00ff00", "#00ff00"),
            border_width=1,
            border_color=("#333333", "#333333")
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=(0, 15))

        # Bouton clear
        clear_btn = ctk.CTkButton(
            log_frame,
            text="🗑️ Clear Logs",
            command=self.clear_logs,
            height=30,
            fg_color=("#666666", "#666666")
        )
        clear_btn.pack(pady=(0, 15))

    def create_monster_ai_panel(self, parent):
        """Panneau IA MONSTER"""

        # Titre
        title_frame = ctk.CTkFrame(parent, fg_color="transparent")
        title_frame.pack(fill="x", padx=15, pady=(15, 10))

        ctk.CTkLabel(
            title_frame,
            text="🤖 MONSTER AI",
            font=ctk.CTkFont(size=20, weight="bold"),
            text_color=("#ff00ff", "#ff00ff")
        ).pack()

        # === MÉTRIQUES IA MONSTER ===
        ai_metrics_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#ff00ff", "#ff00ff")
        )
        ai_metrics_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            ai_metrics_frame,
            text="📊 AI Metrics",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=("#ff00ff", "#ff00ff")
        ).pack(pady=(15, 10))

        # Confiance Monster
        conf_frame = ctk.CTkFrame(ai_metrics_frame, fg_color=("#440044", "#440044"))
        conf_frame.pack(fill="x", padx=10, pady=5)
        ctk.CTkLabel(conf_frame, text="🧠 Monster Confidence", font=ctk.CTkFont(size=11), text_color=("#ff00ff", "#ff00ff")).pack()
        ctk.CTkLabel(
            conf_frame,
            textvariable=self.monster_confidence,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ff00ff", "#ff00ff")
        ).pack()

        # Pump Detection
        pump_frame = ctk.CTkFrame(ai_metrics_frame, fg_color=("#444400", "#444400"))
        pump_frame.pack(fill="x", padx=10, pady=5)
        ctk.CTkLabel(pump_frame, text="🚀 Pump Detection", font=ctk.CTkFont(size=11), text_color=("#ffff00", "#ffff00")).pack()
        ctk.CTkLabel(
            pump_frame,
            textvariable=self.pump_detection,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ffff00", "#ffff00")
        ).pack()

        # Shitcoin Score
        shit_frame = ctk.CTkFrame(ai_metrics_frame, fg_color=("#004400", "#004400"))
        shit_frame.pack(fill="x", padx=10, pady=5)
        ctk.CTkLabel(shit_frame, text="💩 Shitcoin Score", font=ctk.CTkFont(size=11), text_color=("#00ff00", "#00ff00")).pack()
        ctk.CTkLabel(
            shit_frame,
            textvariable=self.shitcoin_score,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#00ff00", "#00ff00")
        ).pack()

        # Biggest Pump
        biggest_frame = ctk.CTkFrame(ai_metrics_frame, fg_color=("#440000", "#440000"))
        biggest_frame.pack(fill="x", padx=10, pady=(5, 15))
        ctk.CTkLabel(biggest_frame, text="🔥 Biggest Pump", font=ctk.CTkFont(size=11), text_color=("#ff0000", "#ff0000")).pack()
        ctk.CTkLabel(
            biggest_frame,
            textvariable=self.biggest_pump,
            font=ctk.CTkFont(size=16, weight="bold"),
            text_color=("#ff0000", "#ff0000")
        ).pack()

        # === MONSTER PAIRS ===
        pairs_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#00ffff", "#00ffff")
        )
        pairs_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            pairs_frame,
            text="💰 Monster Pairs",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=("#00ffff", "#00ffff")
        ).pack(pady=(15, 10))

        # Liste des pairs
        self.pairs_text = ctk.CTkTextbox(
            pairs_frame,
            height=120,
            font=ctk.CTkFont(size=10),
            fg_color=("#000000", "#000000"),
            text_color=("#00ffff", "#00ffff")
        )
        self.pairs_text.pack(fill="x", padx=10, pady=(0, 15))

        # Remplir les pairs
        pairs_list = "\n".join([f"• {pair}" for pair in config.MONSTER_PAIRS])
        self.pairs_text.insert("1.0", pairs_list)

        # === ACTIONS MONSTER ===
        actions_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=1,
            border_color=("#ff6600", "#ff6600")
        )
        actions_frame.pack(fill="x", padx=15, pady=10)

        ctk.CTkLabel(
            actions_frame,
            text="🔧 Monster Actions",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=("#ff6600", "#ff6600")
        ).pack(pady=(15, 10))

        optimize_btn = ctk.CTkButton(
            actions_frame,
            text="⚡ Optimize Monster",
            command=self.optimize_monster,
            height=35,
            fg_color=("#ff6600", "#ff6600"),
            hover_color=("#ff8800", "#ff8800")
        )
        optimize_btn.pack(fill="x", padx=10, pady=2)

        hunt_btn = ctk.CTkButton(
            actions_frame,
            text="🚀 Hunt Pumps",
            command=self.hunt_pumps,
            height=35,
            fg_color=("#ffff00", "#ffff00"),
            hover_color=("#ffff44", "#ffff44"),
            text_color=("#000000", "#000000")
        )
        hunt_btn.pack(fill="x", padx=10, pady=2)

        scan_btn = ctk.CTkButton(
            actions_frame,
            text="💩 Scan Shitcoins",
            command=self.scan_shitcoins,
            height=35,
            fg_color=("#ff00ff", "#ff00ff"),
            hover_color=("#ff44ff", "#ff44ff")
        )
        scan_btn.pack(fill="x", padx=10, pady=2)

        reset_btn = ctk.CTkButton(
            actions_frame,
            text="🔄 Reset Monster",
            command=self.reset_monster,
            height=35,
            fg_color=("#666666", "#666666"),
            hover_color=("#888888", "#888888")
        )
        reset_btn.pack(fill="x", padx=10, pady=(2, 15))

        # === BEAST MODE STATUS ===
        beast_frame = ctk.CTkFrame(
            parent,
            fg_color=("#2a2a2a", "#2a2a2a"),
            border_width=2,
            border_color=("#ff0000", "#ff0000")
        )
        beast_frame.pack(fill="both", expand=True, padx=15, pady=10)

        ctk.CTkLabel(
            beast_frame,
            text="👹 BEAST MODE STATUS",
            font=ctk.CTkFont(size=14, weight="bold"),
            text_color=("#ff0000", "#ff0000")
        ).pack(pady=(15, 10))

        self.beast_status = ctk.CTkTextbox(
            beast_frame,
            font=ctk.CTkFont(size=10),
            fg_color=("#000000", "#000000"),
            text_color=("#ff0000", "#ff0000")
        )
        self.beast_status.pack(fill="both", expand=True, padx=10, pady=(0, 15))

        # Status initial
        initial_status = """🔴 BEAST MODE: INACTIVE
😴 Monster is sleeping...
⚡ Lightning scalp: Ready
🚀 Pump hunter: Scanning
💩 Shitcoin hunter: Waiting
🎯 Accuracy: Building confidence
📊 Volume: Monitoring markets
🔥 Ready to unleash chaos!"""

        self.beast_status.insert("1.0", initial_status)

    # === MÉTHODES MONSTER ===

    def toggle_dex_mode(self):
        """Basculer mode DEX"""
        if self.dex_mode.get():
            self.log_monster("🔗 DEX Mode activé - FREEDOM TRADING!")
            self.log_monster("🚀 No KYC, No limits, Pure DeFi!")
        else:
            self.log_monster("🔗 DEX Mode désactivé")

    def toggle_beast_mode(self):
        """Basculer Beast Mode"""
        if self.beast_mode.get():
            result = messagebox.askyesno(
                "👹 BEAST MODE WARNING",
                "⚠️ ATTENTION: BEAST MODE ACTIVATION!\n\n"
                "Le Monster va trader de manière ULTRA-AGRESSIVE!\n"
                "Scalping rapide, pumps, shitcoins...\n\n"
                "Êtes-vous prêt à unleash the beast?",
                icon="warning"
            )

            if result:
                self.beast_mode_active = True
                monster_ai.beast_mode = True
                self.beast_indicator.configure(
                    text="👹 BEAST UNLEASHED",
                    text_color=("#ff0000", "#ff0000")
                )
                self.log_monster("👹 BEAST MODE ACTIVATED!")
                self.log_monster("🔥 Monster unleashed - CHAOS MODE!")
            else:
                self.beast_mode.set(False)
        else:
            self.beast_mode_active = False
            monster_ai.beast_mode = False
            self.beast_indicator.configure(
                text="😴 SLEEPING",
                text_color=("#666666", "#666666")
            )
            self.log_monster("😴 Beast Mode désactivé")

    def toggle_monster(self):
        """Démarrer/Arrêter le Monster"""
        if not self.is_running:
            self.start_monster()
        else:
            self.stop_monster()

    def start_monster(self):
        """Démarrer le Monster"""
        try:
            self.is_running = True
            self.start_button.configure(
                text="🛑 STOP THE MONSTER",
                fg_color=("#cc0000", "#cc0000"),
                hover_color=("#ff0000", "#ff0000")
            )
            self.status_text.set("🤖 MONSTER ACTIVE")
            self.status_label.configure(text_color=("#00ff00", "#00ff00"))

            if self.beast_mode_active:
                self.beast_indicator.configure(
                    text="👹 BEAST HUNTING",
                    text_color=("#ff0000", "#ff0000")
                )
            else:
                self.beast_indicator.configure(
                    text="🤖 MONSTER ACTIVE",
                    text_color=("#00ff00", "#00ff00")
                )

            mode = "DEX" if self.dex_mode.get() else "SIMULATION"
            self.log_monster(f"🚀 MONSTER UNLEASHED en mode {mode}")
            self.log_monster("👹 Monster modules activés:")

            if self.pump_hunter.get():
                self.log_monster("  🚀 Pump Hunter: SCANNING")
            if self.shitcoin_hunter.get():
                self.log_monster("  💩 Shitcoin Hunter: ACTIVE")
            if self.lightning_scalp.get():
                self.log_monster("  ⚡ Lightning Scalp: READY")
            if self.beast_mode_active:
                self.log_monster("  👹 Beast Mode: UNLEASHED!")

            # Démarrer la simulation Monster
            self.start_monster_simulation()

        except Exception as e:
            self.log_monster(f"❌ Erreur Monster: {e}")
            messagebox.showerror("Erreur", f"Erreur démarrage Monster:\n{e}")

    def stop_monster(self):
        """Arrêter le Monster"""
        self.is_running = False
        self.start_button.configure(
            text="🚀 UNLEASH THE MONSTER",
            fg_color=("#006600", "#006600"),
            hover_color=("#008800", "#008800")
        )
        self.status_text.set("🔴 MONSTER SLEEPING")
        self.status_label.configure(text_color=("#ff0000", "#ff0000"))
        self.beast_indicator.configure(
            text="😴 SLEEPING",
            text_color=("#666666", "#666666")
        )
        self.log_monster("😴 Monster stopped - Going to sleep...")

    def emergency_stop(self):
        """Arrêt d'urgence Monster"""
        result = messagebox.askyesno(
            "🛑 EMERGENCY STOP",
            "ARRÊT D'URGENCE DU MONSTER?\n\n"
            "Cela va immédiatement arrêter\n"
            "tous les trades et fermer les positions!",
            icon="warning"
        )

        if result:
            self.stop_monster()
            self.log_monster("🛑 EMERGENCY STOP ACTIVATED!")
            self.log_monster("🚨 All Monster activities stopped!")
            messagebox.showinfo("Emergency Stop", "Monster stopped successfully!")

    def optimize_monster(self):
        """Optimiser le Monster"""
        self.log_monster("⚡ Optimizing Monster AI...")

        def optimize():
            time.sleep(3)
            monster_ai.confidence = min(0.95, monster_ai.confidence + 0.05)
            monster_ai.pump_detection_rate = min(0.95, monster_ai.pump_detection_rate + 0.03)

            self.root.after(0, lambda: self.log_monster("✅ Monster AI optimized!"))
            self.root.after(0, lambda: self.log_monster("🔥 Enhanced pump detection!"))
            self.root.after(0, lambda: messagebox.showinfo("Optimization", "Monster AI optimized!"))

        threading.Thread(target=optimize, daemon=True).start()

    def hunt_pumps(self):
        """Chasser les pumps"""
        self.log_monster("🚀 Hunting for pumps...")

        def hunt():
            time.sleep(2)
            found_pumps = random.randint(1, 5)
            pump_symbols = random.sample(config.MONSTER_PAIRS, min(found_pumps, len(config.MONSTER_PAIRS)))

            hunt_result = f"🚀 PUMP HUNT RESULTS:\n"
            for symbol in pump_symbols:
                pump_strength = random.uniform(0.05, 0.25)
                hunt_result += f"  • {symbol}: +{pump_strength*100:.1f}% pump detected!\n"

            self.root.after(0, lambda: self.log_monster("✅ Pump hunt completed!"))
            self.root.after(0, lambda: self.log_monster(hunt_result))

        threading.Thread(target=hunt, daemon=True).start()

    def scan_shitcoins(self):
        """Scanner les shitcoins"""
        self.log_monster("💩 Scanning shitcoins...")

        def scan():
            time.sleep(4)
            shitcoins = ["PEPE", "SHIB", "DOGE", "FLOKI", "BONK", "WIF", "BOME"]
            selected = random.sample(shitcoins, random.randint(2, 4))

            scan_result = f"💩 SHITCOIN SCAN RESULTS:\n"
            for coin in selected:
                volatility = random.uniform(0.1, 0.5)
                volume_spike = random.uniform(1.5, 5.0)
                scan_result += f"  • {coin}: Vol={volatility*100:.1f}%, Spike={volume_spike:.1f}x\n"

            self.root.after(0, lambda: self.log_monster("✅ Shitcoin scan completed!"))
            self.root.after(0, lambda: self.log_monster(scan_result))

        threading.Thread(target=scan, daemon=True).start()

    def reset_monster(self):
        """Reset du Monster"""
        result = messagebox.askyesno("Reset Monster", "Reset du Monster AI?")
        if result:
            global monster_ai
            monster_ai = MonsterAI()
            self.scalps_count = 0
            self.successful_scalps = 0
            self.daily_pnl_num = 0.0
            self.total_volume_traded = 0.0
            self.update_monster_display()
            self.log_monster("🔄 Monster AI reset!")
            self.log_monster("👹 Ready for new chaos!")

    def start_monster_simulation(self):
        """Simulation Monster ultra-rapide"""
        def simulation():
            while self.is_running:
                try:
                    # Mise à jour des métriques Monster
                    self.root.after(0, self.update_monster_metrics)

                    # Simulation de scalp ultra-rapide
                    if random.random() < 0.4:  # 40% de chance (plus agressif)
                        self.root.after(0, self.simulate_monster_scalp)

                    # Cycle ultra-rapide (2 secondes)
                    time.sleep(2)
                except:
                    break

        threading.Thread(target=simulation, daemon=True).start()

    def update_monster_metrics(self):
        """Mettre à jour les métriques Monster"""
        try:
            status = monster_ai.get_monster_status()

            # Métriques principales
            self.monster_confidence.set(f"{status['confidence']*100:.1f}%")
            self.pump_detection.set(f"{status['pump_detection_rate']*100:.1f}%")
            self.shitcoin_score.set(f"{status['shitcoin_score']*100:.1f}%")
            self.biggest_pump.set(f"{status['biggest_pump']*100:.1f}%")

            # Beast Mode Status
            if status['beast_mode']:
                beast_text = """🔥 BEAST MODE: ACTIVE
👹 Monster is hunting aggressively!
⚡ Ultra-fast scalping enabled
🚀 Pump detection: MAXIMUM
💩 Shitcoin hunting: ACTIVE
🎯 Accuracy: """ + f"{status['scalp_accuracy']*100:.1f}%" + """
📊 Total scalps: """ + str(status['total_scalps']) + """
🔥 CHAOS MODE UNLEASHED!"""
            else:
                beast_text = f"""🤖 MONSTER MODE: ACTIVE
😎 Monster is trading smartly
⚡ Lightning scalp: {"ON" if status['modes']['lightning_scalp'] else "OFF"}
🚀 Pump hunter: {"ON" if status['modes']['pump_hunter'] else "OFF"}
💩 Shitcoin hunter: {"ON" if status['modes']['shitcoin_hunter'] else "OFF"}
🎯 Accuracy: {status['scalp_accuracy']*100:.1f}%
📊 Total scalps: {status['total_scalps']}
💰 Ready for opportunities!"""

            self.beast_status.delete("1.0", "end")
            self.beast_status.insert("1.0", beast_text)

        except Exception as e:
            print(f"Erreur update Monster metrics: {e}")

    def simulate_monster_scalp(self):
        """Simuler un scalp Monster"""
        try:
            symbol = random.choice(config.MONSTER_PAIRS)

            # Obtenir une décision Monster
            decision = monster_ai.scalp_analysis(symbol)

            if decision:
                self.scalps_count += 1

                # Calculer la probabilité de succès
                base_success = decision['confidence'] * decision['strength']

                # Bonus Beast Mode
                if self.beast_mode_active:
                    base_success *= 1.2

                # Bonus Pump
                if decision['pump_detected']:
                    base_success *= 1.5

                # Bonus Shitcoin
                if decision['analysis']['is_shitcoin']:
                    base_success *= 1.3

                # Bonus Volume Spike
                if decision['analysis']['volume_spike']:
                    base_success *= 1.1

                final_success_prob = min(0.9, base_success)
                is_success = random.random() < final_success_prob

                if is_success:
                    self.successful_scalps += 1
                    # Gains Monster (plus élevés)
                    base_profit = random.uniform(10, 50)

                    # Multiplicateurs Monster
                    if decision['pump_detected']:
                        base_profit *= random.uniform(2, 5)  # Pump bonus
                    if decision['analysis']['is_shitcoin']:
                        base_profit *= random.uniform(1.5, 3)  # Shitcoin bonus
                    if self.beast_mode_active:
                        base_profit *= random.uniform(1.2, 2)  # Beast bonus

                    pnl = base_profit * decision['strength'] * decision['confidence']
                else:
                    # Pertes Monster (limitées par l'IA)
                    base_loss = random.uniform(5, 25)
                    pnl = -base_loss * decision['strength']

                self.daily_pnl_num += pnl
                self.portfolio_value_num += pnl
                self.total_volume_traded += abs(pnl) * 10  # Volume simulé

                # Apprentissage Monster
                scalp_result = {
                    'profitable': is_success,
                    'pnl': pnl,
                    'pump_detected': decision['pump_detected'],
                    'shitcoin': decision['analysis']['is_shitcoin'],
                    'beast_mode': self.beast_mode_active
                }
                monster_ai.learn_from_scalp(scalp_result)

                # Mise à jour affichage
                self.update_monster_display()

                # Log Monster détaillé
                action_emoji = "✅ MONSTER GAIN" if is_success else "❌ MONSTER LOSS"
                pump_info = " 🚀PUMP" if decision['pump_detected'] else ""
                shit_info = " 💩SHIT" if decision['analysis']['is_shitcoin'] else ""
                beast_info = " 👹BEAST" if self.beast_mode_active else ""

                self.log_monster(f"{action_emoji} {symbol}: {pnl:+.2f}€{pump_info}{shit_info}{beast_info}")
                self.log_monster(f"  📊 Conf: {decision['confidence']:.1%} | Vol: {decision['volume_ratio']:.1f}x | Hold: {decision['hold_time']:.0f}s")

                # Log spécial pour les gros gains
                if is_success and pnl > 100:
                    self.log_monster(f"🔥 BIG WIN! {symbol} +{pnl:.2f}€ - MONSTER POWER!")

                # Log spécial pour les pumps détectés
                if decision['pump_detected']:
                    self.log_monster(f"🚀 PUMP DETECTED! {symbol} Score: {decision['pump_score']:.2f}")

        except Exception as e:
            print(f"Erreur simulate Monster scalp: {e}")

    def update_monster_display(self):
        """Mettre à jour l'affichage Monster"""
        try:
            self.total_scalps.set(str(self.scalps_count))
            self.portfolio_value.set(f"${self.portfolio_value_num:,.2f}")
            self.volume_traded.set(f"${self.total_volume_traded:,.0f}")

            # P&L avec couleurs Monster
            if self.daily_pnl_num >= 0:
                pnl_color = ("#00ff00", "#00ff00")  # Vert Monster
            else:
                pnl_color = ("#ff0000", "#ff0000")  # Rouge Monster

            self.daily_pnl.set(f"${self.daily_pnl_num:,.2f}")
            self.pnl_label.configure(text_color=pnl_color)

            # Accuracy Monster
            if self.scalps_count > 0:
                accuracy = (self.successful_scalps / self.scalps_count) * 100
                self.scalp_accuracy.set(f"{accuracy:.1f}%")

                # Couleur selon accuracy
                if accuracy >= 80:
                    acc_color = ("#00ff00", "#00ff00")  # Vert excellent
                elif accuracy >= 60:
                    acc_color = ("#ffff00", "#ffff00")  # Jaune bon
                else:
                    acc_color = ("#ff6600", "#ff6600")  # Orange moyen

                # Trouver le label accuracy et changer sa couleur
                for widget in self.root.winfo_children():
                    if hasattr(widget, 'winfo_children'):
                        self._update_accuracy_color(widget, acc_color)

        except Exception as e:
            print(f"Erreur update Monster display: {e}")

    def _update_accuracy_color(self, widget, color):
        """Mettre à jour la couleur de l'accuracy récursivement"""
        try:
            for child in widget.winfo_children():
                if hasattr(child, 'cget') and hasattr(child, 'configure'):
                    try:
                        if child.cget('textvariable') == str(self.scalp_accuracy):
                            child.configure(text_color=color)
                    except:
                        pass
                if hasattr(child, 'winfo_children'):
                    self._update_accuracy_color(child, color)
        except:
            pass

    def start_monster_animations(self):
        """Démarrer les animations Monster"""
        def animate():
            while True:
                try:
                    if self.is_running:
                        # Animation du titre
                        current_time = time.time()
                        if int(current_time) % 2 == 0:
                            title_color = ("#00ff00", "#00ff00")
                        else:
                            title_color = ("#00ff44", "#00ff44")

                        # Animation du status
                        if self.beast_mode_active and self.is_running:
                            if int(current_time * 2) % 2 == 0:
                                status_color = ("#ff0000", "#ff0000")
                            else:
                                status_color = ("#ff4444", "#ff4444")
                            self.status_label.configure(text_color=status_color)

                    time.sleep(0.5)
                except:
                    break

        threading.Thread(target=animate, daemon=True).start()

    def log_monster(self, message):
        """Logger Monster avec couleurs"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.insert("end", log_entry)
            self.log_text.see("end")

            # Limiter les lignes
            lines = self.log_text.get("1.0", "end").split("\n")
            if len(lines) > 200:
                self.log_text.delete("1.0", "50.0")

        except Exception as e:
            print(f"Erreur log Monster: {e}")

    def clear_logs(self):
        """Effacer les logs Monster"""
        try:
            self.log_text.delete("1.0", "end")
            self.log_monster("🗑️ Monster logs cleared")
        except:
            pass

    def on_closing(self):
        """Gestionnaire de fermeture Monster"""
        if self.is_running:
            result = messagebox.askyesno(
                "Fermeture Monster",
                "Le Monster est en cours de trading.\n"
                "L'arrêter et fermer l'application?",
                icon="warning"
            )
            if result:
                self.stop_monster()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """Lancer l'application Monster"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Messages de démarrage Monster
        self.log_monster("🤖👹 INVESTT MONSTER BOT démarré")
        self.log_monster("🔥 Beast Mode Trading System initialisé")
        self.log_monster("✅ Monster modules disponibles:")
        self.log_monster("  🚀 Pump Hunter - Détection ultra-rapide")
        self.log_monster("  💩 Shitcoin Hunter - Volatilité maximale")
        self.log_monster("  ⚡ Lightning Scalp - Trades millisecondes")
        self.log_monster("  🔗 DEX Trading - Freedom mode")
        self.log_monster("  👹 Beast Mode - Chaos unleashed")
        self.log_monster("💡 Design de MALADE avec IA Monster")
        self.log_monster("🚀 Configurez vos paramètres et UNLEASH THE MONSTER!")

        self.root.mainloop()


def main():
    """Point d'entrée Monster"""
    try:
        app = MonsterTradingApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur Monster", f"Erreur critique Monster:\n{e}")


if __name__ == "__main__":
    main()
