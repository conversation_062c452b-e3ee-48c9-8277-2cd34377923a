"""
🚀 INVESTT Trading Bot - Créateur d'Installateur Windows Professionnel
Crée un vrai fichier .exe d'installation
"""
import os
import sys
import subprocess
from pathlib import Path
import shutil

def install_build_tools():
    """Installer les outils de build nécessaires"""
    print("🔧 Installation des outils de build...")
    
    tools = [
        "pyinstaller",
        "auto-py-to-exe", 
        "nsis",
        "cx_Freeze"
    ]
    
    for tool in tools:
        try:
            print(f"   📦 Installation de {tool}...", end=" ")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", tool],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅")
            else:
                print("❌")
                
        except Exception as e:
            print(f"❌ {e}")

def create_setup_script():
    """Créer le script de setup pour cx_Freeze"""
    
    setup_content = '''"""
Setup script pour créer l'installateur INVESTT Trading Bot
"""
import sys
from cx_Freeze import setup, Executable
import os

# Dépendances à inclure
packages = [
    "customtkinter",
    "pandas", 
    "numpy",
    "ccxt",
    "sqlalchemy",
    "loguru",
    "plotly",
    "PIL",
    "websocket",
    "dotenv",
    "threading",
    "tkinter"
]

# Fichiers à inclure
include_files = [
    ("config.py", "config.py"),
    ("src/", "src/"),
    (".env.example", ".env.example"),
    ("README.md", "README.md"),
    ("LIVE_TRADING_GUIDE.md", "LIVE_TRADING_GUIDE.md")
]

# Options de build
build_exe_options = {
    "packages": packages,
    "include_files": include_files,
    "excludes": ["test", "unittest"],
    "zip_include_packages": ["encodings", "PySide6"],
    "include_msvcrt": True
}

# Configuration de l'exécutable
exe = Executable(
    script="investt_app.py",
    base="Win32GUI",  # Pour une app Windows (pas de console)
    target_name="INVESTT_Trading_Bot.exe",
    icon="assets/icon.ico" if os.path.exists("assets/icon.ico") else None,
    shortcut_name="INVESTT Trading Bot",
    shortcut_dir="DesktopFolder"
)

# Setup
setup(
    name="INVESTT Trading Bot",
    version="1.0.0",
    description="Agent IA de Trading Automatisé",
    author="INVESTT Team",
    options={"build_exe": build_exe_options},
    executables=[exe]
)
'''
    
    with open("setup.py", "w") as f:
        f.write(setup_content)
    
    print("✅ Script setup.py créé")

def create_nsis_installer():
    """Créer un script NSIS pour l'installateur Windows"""
    
    nsis_content = '''
; INVESTT Trading Bot - Installateur NSIS
!define APPNAME "INVESTT Trading Bot"
!define COMPANYNAME "INVESTT"
!define DESCRIPTION "Agent IA de Trading Automatisé"
!define VERSIONMAJOR 1
!define VERSIONMINOR 0
!define VERSIONBUILD 0

!define HELPURL "https://github.com/investt/trading-bot"
!define UPDATEURL "https://github.com/investt/trading-bot/releases"
!define ABOUTURL "https://github.com/investt/trading-bot"

!define INSTALLSIZE 150000  ; Taille estimée en KB

RequestExecutionLevel admin

InstallDir "$PROGRAMFILES\\${APPNAME}"

Name "${APPNAME}"
Icon "assets\\icon.ico"
outFile "INVESTT_Trading_Bot_Installer.exe"

!include LogicLib.nsh

page components
page directory
page instfiles

!macro VerifyUserIsAdmin
UserInfo::GetAccountType
pop $0
${If} $0 != "admin"
    messageBox mb_iconstop "Droits administrateur requis!"
    setErrorLevel 740
    quit
${EndIf}
!macroend

function .onInit
    setShellVarContext all
    !insertmacro VerifyUserIsAdmin
functionEnd

section "INVESTT Trading Bot (requis)"
    sectionIn RO
    setOutPath $INSTDIR
    
    ; Copier tous les fichiers
    file /r "dist\\INVESTT_Trading_Bot\\*"
    
    ; Créer le raccourci menu démarrer
    createDirectory "$SMPROGRAMS\\${APPNAME}"
    createShortCut "$SMPROGRAMS\\${APPNAME}\\${APPNAME}.lnk" "$INSTDIR\\INVESTT_Trading_Bot.exe"
    createShortCut "$SMPROGRAMS\\${APPNAME}\\Désinstaller.lnk" "$INSTDIR\\uninstall.exe"
    
    ; Créer le raccourci bureau
    createShortCut "$DESKTOP\\${APPNAME}.lnk" "$INSTDIR\\INVESTT_Trading_Bot.exe"
    
    ; Créer le désinstalleur
    writeUninstaller "$INSTDIR\\uninstall.exe"
    
    ; Ajouter aux programmes installés
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayName" "${APPNAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "UninstallString" "$INSTDIR\\uninstall.exe"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "InstallLocation" "$INSTDIR"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayIcon" "$INSTDIR\\INVESTT_Trading_Bot.exe"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "Publisher" "${COMPANYNAME}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "HelpLink" "${HELPURL}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "URLUpdateInfo" "${UPDATEURL}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "URLInfoAbout" "${ABOUTURL}"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "DisplayVersion" "${VERSIONMAJOR}.${VERSIONMINOR}.${VERSIONBUILD}"
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "VersionMajor" ${VERSIONMAJOR}
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "VersionMinor" ${VERSIONMINOR}
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "NoModify" 1
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "NoRepair" 1
    WriteRegDWORD HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}" "EstimatedSize" ${INSTALLSIZE}
    
sectionEnd

section "Désinstalleur"
    delete "$INSTDIR\\uninstall.exe"
    delete "$DESKTOP\\${APPNAME}.lnk"
    rmDir /r "$SMPROGRAMS\\${APPNAME}"
    rmDir /r "$INSTDIR"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${APPNAME}"
sectionEnd
'''
    
    with open("installer.nsi", "w") as f:
        f.write(nsis_content)
    
    print("✅ Script NSIS créé")

def create_icon():
    """Créer une icône pour l'application"""
    icon_content = '''
import tkinter as tk
from PIL import Image, ImageDraw, ImageFont
import os

def create_icon():
    # Créer une image 256x256
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Fond dégradé bleu
    for i in range(size):
        color = (0, int(100 + i * 155 / size), int(200 + i * 55 / size), 255)
        draw.rectangle([0, i, size, i+1], fill=color)
    
    # Cercle principal
    margin = 20
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(255, 255, 255, 200), outline=(0, 50, 150, 255), width=8)
    
    # Texte "I"
    try:
        font = ImageFont.truetype("arial.ttf", 120)
    except:
        font = ImageFont.load_default()
    
    text = "I"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2 - 10
    
    draw.text((x, y), text, fill=(0, 50, 150, 255), font=font)
    
    # Sauvegarder
    os.makedirs("assets", exist_ok=True)
    img.save("assets/icon.png")
    
    # Convertir en ICO si possible
    try:
        img.save("assets/icon.ico", format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ Icône créée: assets/icon.ico")
    except:
        print("✅ Icône créée: assets/icon.png")

if __name__ == "__main__":
    create_icon()
'''
    
    with open("create_icon.py", "w") as f:
        f.write(icon_content)
    
    # Exécuter la création d'icône
    try:
        subprocess.run([sys.executable, "create_icon.py"], check=True)
    except:
        print("⚠️ Icône non créée (optionnel)")

def build_executable():
    """Construire l'exécutable avec cx_Freeze"""
    print("\n🔨 Construction de l'exécutable...")
    
    try:
        result = subprocess.run(
            [sys.executable, "setup.py", "build"],
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode == 0:
            print("✅ Exécutable créé dans le dossier 'build'")
            return True
        else:
            print(f"❌ Erreur build: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_installer():
    """Créer l'installateur NSIS"""
    print("\n📦 Création de l'installateur...")
    
    # Vérifier si NSIS est installé
    nsis_path = None
    possible_paths = [
        r"C:\Program Files (x86)\NSIS\makensis.exe",
        r"C:\Program Files\NSIS\makensis.exe",
        "makensis.exe"  # Si dans le PATH
    ]
    
    for path in possible_paths:
        if shutil.which(path) or os.path.exists(path):
            nsis_path = path
            break
    
    if not nsis_path:
        print("❌ NSIS non trouvé")
        print("📥 Téléchargez NSIS depuis: https://nsis.sourceforge.io/")
        return False
    
    try:
        result = subprocess.run(
            [nsis_path, "installer.nsi"],
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("✅ Installateur créé: INVESTT_Trading_Bot_Installer.exe")
            return True
        else:
            print(f"❌ Erreur NSIS: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 CRÉATION D'UN INSTALLATEUR WINDOWS PROFESSIONNEL")
    print("=" * 60)
    
    # Étapes
    steps = [
        ("Installation des outils", install_build_tools),
        ("Création de l'icône", create_icon),
        ("Création du script setup", create_setup_script),
        ("Création du script NSIS", create_nsis_installer),
        ("Construction de l'exécutable", build_executable),
        ("Création de l'installateur", create_installer)
    ]
    
    for step_name, step_func in steps:
        print(f"\n🔄 {step_name}...")
        try:
            success = step_func()
            if step_func in [build_executable, create_installer] and not success:
                print(f"⚠️ Échec de {step_name}")
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 PROCESSUS TERMINÉ!")
    print("=" * 60)
    
    if os.path.exists("INVESTT_Trading_Bot_Installer.exe"):
        print("✅ Installateur créé avec succès!")
        print("📁 Fichier: INVESTT_Trading_Bot_Installer.exe")
        print("\n🚀 Instructions:")
        print("   1. Double-cliquez sur INVESTT_Trading_Bot_Installer.exe")
        print("   2. Suivez l'assistant d'installation")
        print("   3. L'application sera installée dans Program Files")
        print("   4. Un raccourci sera créé sur le bureau")
        print("   5. Lancez depuis le menu Démarrer ou le bureau")
    else:
        print("⚠️ Installateur non créé")
        print("💡 Vérifiez que tous les outils sont installés")
        print("   - Python 3.8+")
        print("   - NSIS (https://nsis.sourceforge.io/)")
        print("   - Tous les fichiers du projet INVESTT")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Processus interrompu")
    except Exception as e:
        print(f"\n\n❌ Erreur critique: {e}")
    finally:
        input("\nAppuyez sur Entrée pour fermer...")
