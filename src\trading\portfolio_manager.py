"""
Portfolio management and tracking
"""
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import pandas as pd

from config import config
from src.utils.logger import log_performance, log_error, logger
from src.database import session_scope, Trade, Position, PerformanceMetrics
from src.data import market_data_manager


@dataclass
class PortfolioPosition:
    """Portfolio position information"""
    symbol: str
    side: str  # 'long' or 'short'
    amount: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    unrealized_pnl_percentage: float
    value: float
    entry_time: datetime


@dataclass
class PortfolioSummary:
    """Portfolio summary information"""
    total_value: float
    cash_balance: float
    positions_value: float
    daily_pnl: float
    daily_pnl_percentage: float
    total_pnl: float
    total_pnl_percentage: float
    positions_count: int
    largest_position: float
    largest_loss: float


class PortfolioManager:
    """Manages portfolio positions and performance tracking"""
    
    def __init__(self):
        self.initial_capital = config.INITIAL_CAPITAL
        self.cash_balance = self.initial_capital
        self.positions: Dict[str, PortfolioPosition] = {}
        self.daily_start_value = self.initial_capital
        
        # Performance tracking
        self.performance_history = []
        self.max_portfolio_value = self.initial_capital
        self.max_drawdown = 0.0
        
    def initialize(self):
        """Initialize portfolio from database"""
        try:
            # Load existing positions
            with session_scope() as session:
                db_positions = session.query(Position).all()
                for pos in db_positions:
                    self.positions[pos.symbol] = PortfolioPosition(
                        symbol=pos.symbol,
                        side=pos.side,
                        amount=pos.amount,
                        entry_price=pos.entry_price,
                        current_price=pos.current_price,
                        unrealized_pnl=pos.unrealized_pnl,
                        unrealized_pnl_percentage=pos.unrealized_pnl_percentage,
                        value=pos.amount * pos.current_price,
                        entry_time=pos.opened_at
                    )
                
                # Get latest performance metrics
                latest_performance = session.query(PerformanceMetrics).order_by(
                    PerformanceMetrics.date.desc()
                ).first()
                
                if latest_performance:
                    self.cash_balance = latest_performance.cash_balance
                    self.max_portfolio_value = latest_performance.total_value
                    self.max_drawdown = latest_performance.max_drawdown
                    
                    # Set daily start value if it's a new day
                    today = datetime.utcnow().date()
                    if latest_performance.date.date() == today:
                        self.daily_start_value = latest_performance.total_value
                    else:
                        self.daily_start_value = self.get_total_value()
            
            logger.info(f"Portfolio initialized: {len(self.positions)} positions, "
                       f"cash: {self.cash_balance:.2f}, total value: {self.get_total_value():.2f}")
            
        except Exception as e:
            log_error("Error initializing portfolio", e)
    
    def update_position(self, symbol: str, side: str, amount: float, price: float):
        """Update or create a position"""
        try:
            if symbol in self.positions:
                # Update existing position (average price calculation)
                existing = self.positions[symbol]
                
                if existing.side == side:
                    # Same side - average the prices
                    total_amount = existing.amount + amount
                    total_value = (existing.amount * existing.entry_price) + (amount * price)
                    new_entry_price = total_value / total_amount
                    
                    self.positions[symbol] = PortfolioPosition(
                        symbol=symbol,
                        side=side,
                        amount=total_amount,
                        entry_price=new_entry_price,
                        current_price=price,
                        unrealized_pnl=0.0,
                        unrealized_pnl_percentage=0.0,
                        value=total_amount * price,
                        entry_time=existing.entry_time
                    )
                else:
                    # Opposite side - close or reduce position
                    if amount >= existing.amount:
                        # Close position completely
                        realized_pnl = self._calculate_realized_pnl(existing, amount, price)
                        self.cash_balance += realized_pnl
                        del self.positions[symbol]
                        
                        # If amount > existing.amount, create new position
                        if amount > existing.amount:
                            remaining_amount = amount - existing.amount
                            self.positions[symbol] = PortfolioPosition(
                                symbol=symbol,
                                side=side,
                                amount=remaining_amount,
                                entry_price=price,
                                current_price=price,
                                unrealized_pnl=0.0,
                                unrealized_pnl_percentage=0.0,
                                value=remaining_amount * price,
                                entry_time=datetime.utcnow()
                            )
                    else:
                        # Reduce position
                        realized_pnl = self._calculate_realized_pnl(existing, amount, price)
                        self.cash_balance += realized_pnl
                        
                        new_amount = existing.amount - amount
                        self.positions[symbol] = PortfolioPosition(
                            symbol=symbol,
                            side=existing.side,
                            amount=new_amount,
                            entry_price=existing.entry_price,
                            current_price=price,
                            unrealized_pnl=0.0,
                            unrealized_pnl_percentage=0.0,
                            value=new_amount * price,
                            entry_time=existing.entry_time
                        )
            else:
                # New position
                self.positions[symbol] = PortfolioPosition(
                    symbol=symbol,
                    side=side,
                    amount=amount,
                    entry_price=price,
                    current_price=price,
                    unrealized_pnl=0.0,
                    unrealized_pnl_percentage=0.0,
                    value=amount * price,
                    entry_time=datetime.utcnow()
                )
            
            # Update cash balance (subtract trade value and fees)
            trade_value = amount * price
            fee = trade_value * 0.001  # 0.1% fee
            
            if side == 'buy':
                self.cash_balance -= (trade_value + fee)
            else:
                self.cash_balance += (trade_value - fee)
            
            # Update database
            self._update_position_in_db(symbol)
            
            log_performance(f"Position updated: {side} {amount} {symbol} at {price}")
            
        except Exception as e:
            log_error(f"Error updating position for {symbol}", e)
    
    def update_current_price(self, symbol: str, current_price: float):
        """Update current price for a position"""
        if symbol in self.positions:
            position = self.positions[symbol]
            
            # Calculate unrealized P&L
            if position.side == 'long':
                unrealized_pnl = (current_price - position.entry_price) * position.amount
            else:  # short
                unrealized_pnl = (position.entry_price - current_price) * position.amount
            
            unrealized_pnl_percentage = unrealized_pnl / (position.entry_price * position.amount)
            
            # Update position
            self.positions[symbol] = PortfolioPosition(
                symbol=position.symbol,
                side=position.side,
                amount=position.amount,
                entry_price=position.entry_price,
                current_price=current_price,
                unrealized_pnl=unrealized_pnl,
                unrealized_pnl_percentage=unrealized_pnl_percentage,
                value=position.amount * current_price,
                entry_time=position.entry_time
            )
    
    def close_position(self, symbol: str, exit_price: float) -> float:
        """Close a position and return realized P&L"""
        if symbol not in self.positions:
            return 0.0
        
        position = self.positions[symbol]
        realized_pnl = self._calculate_realized_pnl(position, position.amount, exit_price)
        
        # Update cash balance
        self.cash_balance += realized_pnl
        
        # Remove position
        del self.positions[symbol]
        
        # Update database
        self._remove_position_from_db(symbol)
        
        log_performance(f"Position closed: {symbol} at {exit_price}, PnL: {realized_pnl:.2f}")
        
        return realized_pnl
    
    def _calculate_realized_pnl(self, position: PortfolioPosition, amount: float, exit_price: float) -> float:
        """Calculate realized P&L for a position"""
        if position.side == 'long':
            return (exit_price - position.entry_price) * amount
        else:  # short
            return (position.entry_price - exit_price) * amount
    
    def get_total_value(self) -> float:
        """Get total portfolio value"""
        positions_value = sum(pos.value for pos in self.positions.values())
        return self.cash_balance + positions_value
    
    def get_positions_value(self) -> float:
        """Get total value of all positions"""
        return sum(pos.value for pos in self.positions.values())
    
    def get_daily_pnl(self) -> float:
        """Get daily P&L"""
        current_value = self.get_total_value()
        return current_value - self.daily_start_value
    
    def get_daily_pnl_percentage(self) -> float:
        """Get daily P&L percentage"""
        daily_pnl = self.get_daily_pnl()
        if self.daily_start_value > 0:
            return daily_pnl / self.daily_start_value
        return 0.0
    
    def get_total_pnl(self) -> float:
        """Get total P&L since inception"""
        return self.get_total_value() - self.initial_capital
    
    def get_total_pnl_percentage(self) -> float:
        """Get total P&L percentage"""
        total_pnl = self.get_total_pnl()
        return total_pnl / self.initial_capital
    
    def get_portfolio_summary(self) -> PortfolioSummary:
        """Get portfolio summary"""
        total_value = self.get_total_value()
        positions_value = self.get_positions_value()
        daily_pnl = self.get_daily_pnl()
        
        # Calculate largest position and loss
        largest_position = 0.0
        largest_loss = 0.0
        
        for position in self.positions.values():
            if position.value > largest_position:
                largest_position = position.value
            if position.unrealized_pnl < largest_loss:
                largest_loss = position.unrealized_pnl
        
        return PortfolioSummary(
            total_value=total_value,
            cash_balance=self.cash_balance,
            positions_value=positions_value,
            daily_pnl=daily_pnl,
            daily_pnl_percentage=self.get_daily_pnl_percentage(),
            total_pnl=self.get_total_pnl(),
            total_pnl_percentage=self.get_total_pnl_percentage(),
            positions_count=len(self.positions),
            largest_position=largest_position,
            largest_loss=largest_loss
        )
    
    def _update_position_in_db(self, symbol: str):
        """Update position in database"""
        try:
            if symbol not in self.positions:
                return
            
            position = self.positions[symbol]
            
            with session_scope() as session:
                db_position = session.query(Position).filter(Position.symbol == symbol).first()
                
                if db_position:
                    # Update existing
                    db_position.side = position.side
                    db_position.amount = position.amount
                    db_position.entry_price = position.entry_price
                    db_position.current_price = position.current_price
                    db_position.unrealized_pnl = position.unrealized_pnl
                    db_position.unrealized_pnl_percentage = position.unrealized_pnl_percentage
                    db_position.updated_at = datetime.utcnow()
                else:
                    # Create new
                    db_position = Position(
                        symbol=position.symbol,
                        side=position.side,
                        amount=position.amount,
                        entry_price=position.entry_price,
                        current_price=position.current_price,
                        unrealized_pnl=position.unrealized_pnl,
                        unrealized_pnl_percentage=position.unrealized_pnl_percentage
                    )
                    session.add(db_position)
                
                session.commit()
                
        except Exception as e:
            log_error(f"Error updating position in database for {symbol}", e)
    
    def _remove_position_from_db(self, symbol: str):
        """Remove position from database"""
        try:
            with session_scope() as session:
                db_position = session.query(Position).filter(Position.symbol == symbol).first()
                if db_position:
                    session.delete(db_position)
                    session.commit()
                    
        except Exception as e:
            log_error(f"Error removing position from database for {symbol}", e)
