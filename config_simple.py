"""
Configuration simplifiée pour INVESTT Trading Bot
Sans dépendance Pydantic pour éviter les problèmes d'import
"""
import os
from dotenv import load_dotenv

# Charger les variables d'environnement
load_dotenv()

def get_env_var(name, default=None, var_type=str):
    """Récupérer une variable d'environnement avec type"""
    value = os.getenv(name)

    # Si pas de variable d'environnement, retourner la valeur par défaut
    if value is None:
        return default

    # Conversion selon le type demandé
    if var_type == bool:
        if isinstance(value, bool):
            return value
        return str(value).lower() in ('true', '1', 'yes', 'on')
    elif var_type == int:
        try:
            return int(value)
        except (ValueError, TypeError):
            return default
    elif var_type == float:
        try:
            return float(value)
        except (ValueError, TypeError):
            return default
    elif var_type == list:
        if isinstance(value, str):
            return [item.strip() for item in value.split(',') if item.strip()]
        elif isinstance(value, list):
            return value
        return default

    return str(value)

class TradingConfig:
    """Configuration simplifiée pour le trading bot"""
    
    def __init__(self):
        # API Configuration
        self.BINANCE_API_KEY = get_env_var("BINANCE_API_KEY", "")
        self.BINANCE_SECRET_KEY = get_env_var("BINANCE_SECRET_KEY", "")
        self.BINANCE_TESTNET = get_env_var("BINANCE_TESTNET", True, bool)
        
        # Database Configuration
        self.DATABASE_URL = get_env_var("DATABASE_URL", "sqlite:///investt.db")
        
        # Trading Parameters
        self.INITIAL_CAPITAL = get_env_var("INITIAL_CAPITAL", 1000.0, float)
        self.MAX_POSITION_SIZE = get_env_var("MAX_POSITION_SIZE", 0.02, float)
        self.MAX_DAILY_LOSS = get_env_var("MAX_DAILY_LOSS", 150.0, float)
        self.MAX_DRAWDOWN = get_env_var("MAX_DRAWDOWN", 0.10, float)
        
        # Risk Management
        self.STOP_LOSS_PERCENTAGE = get_env_var("STOP_LOSS_PERCENTAGE", 0.02, float)
        self.TAKE_PROFIT_PERCENTAGE = get_env_var("TAKE_PROFIT_PERCENTAGE", 0.04, float)
        
        # Strategy Parameters
        self.RSI_PERIOD = get_env_var("RSI_PERIOD", 14, int)
        self.RSI_OVERSOLD = get_env_var("RSI_OVERSOLD", 30.0, float)
        self.RSI_OVERBOUGHT = get_env_var("RSI_OVERBOUGHT", 70.0, float)
        
        self.MACD_FAST = get_env_var("MACD_FAST", 12, int)
        self.MACD_SLOW = get_env_var("MACD_SLOW", 26, int)
        self.MACD_SIGNAL = get_env_var("MACD_SIGNAL", 9, int)
        
        # Timeframes
        self.TIMEFRAMES = get_env_var("TIMEFRAMES", ["1m", "5m", "15m", "1h"], list)
        self.PRIMARY_TIMEFRAME = get_env_var("PRIMARY_TIMEFRAME", "5m")
        
        # Symbols to trade
        self.TRADING_PAIRS = get_env_var("TRADING_PAIRS", ["BTC/USDT", "ETH/USDT", "BNB/USDT"], list)
        
        # Logging
        self.LOG_LEVEL = get_env_var("LOG_LEVEL", "INFO")
        self.LOG_FILE = get_env_var("LOG_FILE", "logs/investt.log")
        
        # Notifications
        self.TELEGRAM_BOT_TOKEN = get_env_var("TELEGRAM_BOT_TOKEN", "")
        self.TELEGRAM_CHAT_ID = get_env_var("TELEGRAM_CHAT_ID", "")
        
        self.EMAIL_SMTP_SERVER = get_env_var("EMAIL_SMTP_SERVER", "")
        self.EMAIL_PORT = get_env_var("EMAIL_PORT", 587, int)
        self.EMAIL_USERNAME = get_env_var("EMAIL_USERNAME", "")
        self.EMAIL_PASSWORD = get_env_var("EMAIL_PASSWORD", "")
        self.EMAIL_TO = get_env_var("EMAIL_TO", "")
        
        # Trading Mode
        self.PAPER_TRADING = get_env_var("PAPER_TRADING", True, bool)
        self.LIVE_TRADING = get_env_var("LIVE_TRADING", False, bool)
        
        # Performance
        self.MAX_CONCURRENT_TRADES = get_env_var("MAX_CONCURRENT_TRADES", 5, int)
        self.ORDER_TIMEOUT = get_env_var("ORDER_TIMEOUT", 30, int)

# Instance globale de configuration
config = TradingConfig()

def get_trading_config():
    """Récupérer la configuration globale"""
    return config

def update_config(**kwargs):
    """Mettre à jour la configuration"""
    global config
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)

# Paramètres de gestion des risques
RISK_PARAMS = {
    'max_daily_loss': config.MAX_DAILY_LOSS,
    'max_position_size': config.MAX_POSITION_SIZE,
    'max_drawdown': config.MAX_DRAWDOWN,
    'correlation_limit': 0.7,
    'volatility_filter': True,
    'max_concurrent_trades': config.MAX_CONCURRENT_TRADES
}

# Configurations des stratégies
STRATEGY_CONFIGS = {
    'rsi_strategy': {
        'period': config.RSI_PERIOD,
        'oversold': config.RSI_OVERSOLD,
        'overbought': config.RSI_OVERBOUGHT,
        'timeframe': config.PRIMARY_TIMEFRAME
    },
    'macd_strategy': {
        'fast_period': config.MACD_FAST,
        'slow_period': config.MACD_SLOW,
        'signal_period': config.MACD_SIGNAL,
        'timeframe': config.PRIMARY_TIMEFRAME
    }
}
