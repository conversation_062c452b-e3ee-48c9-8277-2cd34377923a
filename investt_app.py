"""
🚀 INVESTT Trading Bot - Application Desktop Ultra-Moderne
Interface graphique moderne avec CustomTkinter
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import json
from datetime import datetime
from pathlib import Path
import sys
import os

# Configuration du thème moderne
ctk.set_appearance_mode("dark")  # "dark" ou "light"
ctk.set_default_color_theme("blue")  # "blue", "green", "dark-blue"

# Ajouter le chemin src
sys.path.append(str(Path(__file__).parent))

from config import config
from src.trading import trading_engine
from src.strategies import create_rsi_strategy
from src.database import init_db
from src.utils.logger import logger


class ModernTradingApp:
    """Application Desktop Moderne pour INVESTT Trading Bot"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.setup_trading_engine()
        self.start_update_loop()
    
    def setup_window(self):
        """Configuration de la fenêtre principale"""
        self.root.title("🚀 INVESTT Trading Bot - Live Trading")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Centrer la fenêtre
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        self.root.geometry(f"1400x900+{x}+{y}")
        
        # Icône (optionnel)
        try:
            self.root.iconbitmap("assets/icon.ico")
        except:
            pass
    
    def setup_variables(self):
        """Initialisation des variables"""
        self.is_running = False
        self.is_live_trading = tk.BooleanVar(value=False)
        self.auto_start = tk.BooleanVar(value=False)
        
        # Métriques
        self.portfolio_value = tk.StringVar(value="$0.00")
        self.daily_pnl = tk.StringVar(value="$0.00")
        self.total_trades = tk.StringVar(value="0")
        self.win_rate = tk.StringVar(value="0%")
        self.status_text = tk.StringVar(value="🔴 Arrêté")
        
        # Configuration
        self.capital = tk.StringVar(value=str(config.INITIAL_CAPITAL))
        self.max_loss = tk.StringVar(value=str(config.MAX_DAILY_LOSS))
        self.position_size = tk.StringVar(value=str(config.MAX_POSITION_SIZE * 100))
    
    def create_widgets(self):
        """Création de l'interface utilisateur moderne"""
        
        # === HEADER ===
        header_frame = ctk.CTkFrame(self.root, height=80, corner_radius=0)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # Titre principal
        title_label = ctk.CTkLabel(
            header_frame, 
            text="🚀 INVESTT TRADING BOT", 
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=20)
        
        # Status en temps réel
        self.status_label = ctk.CTkLabel(
            header_frame,
            textvariable=self.status_text,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.status_label.pack(side="right", padx=20, pady=20)
        
        # === MAIN CONTAINER ===
        main_container = ctk.CTkFrame(self.root, corner_radius=0)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # === LEFT PANEL - CONTRÔLES ===
        left_panel = ctk.CTkFrame(main_container, width=350)
        left_panel.pack(side="left", fill="y", padx=(0, 10))
        left_panel.pack_propagate(False)
        
        self.create_control_panel(left_panel)
        
        # === RIGHT PANEL - MONITORING ===
        right_panel = ctk.CTkFrame(main_container)
        right_panel.pack(side="right", fill="both", expand=True)
        
        self.create_monitoring_panel(right_panel)
    
    def create_control_panel(self, parent):
        """Panneau de contrôle moderne"""
        
        # Titre du panneau
        ctk.CTkLabel(
            parent, 
            text="⚙️ CONTRÔLES", 
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=(20, 10))
        
        # === MODE DE TRADING ===
        mode_frame = ctk.CTkFrame(parent)
        mode_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(
            mode_frame, 
            text="💰 Mode de Trading", 
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        # Switch Live/Paper Trading
        mode_switch = ctk.CTkSwitch(
            mode_frame,
            text="LIVE TRADING",
            variable=self.is_live_trading,
            command=self.toggle_trading_mode,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        mode_switch.pack(pady=10)
        
        # Warning pour live trading
        self.warning_label = ctk.CTkLabel(
            mode_frame,
            text="⚠️ ARGENT RÉEL EN JEU !",
            text_color="red",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        
        # === BOUTONS PRINCIPAUX ===
        buttons_frame = ctk.CTkFrame(parent)
        buttons_frame.pack(fill="x", padx=20, pady=20)
        
        # Bouton START/STOP
        self.start_button = ctk.CTkButton(
            buttons_frame,
            text="🚀 DÉMARRER",
            command=self.toggle_bot,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            fg_color="green",
            hover_color="darkgreen"
        )
        self.start_button.pack(fill="x", pady=5)
        
        # Bouton PAUSE
        self.pause_button = ctk.CTkButton(
            buttons_frame,
            text="⏸️ PAUSE",
            command=self.pause_bot,
            font=ctk.CTkFont(size=14),
            height=40,
            state="disabled"
        )
        self.pause_button.pack(fill="x", pady=5)
        
        # Bouton ARRÊT D'URGENCE
        emergency_button = ctk.CTkButton(
            buttons_frame,
            text="🛑 ARRÊT D'URGENCE",
            command=self.emergency_stop,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            fg_color="red",
            hover_color="darkred"
        )
        emergency_button.pack(fill="x", pady=5)
        
        # === CONFIGURATION ===
        config_frame = ctk.CTkFrame(parent)
        config_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(
            config_frame, 
            text="⚙️ Configuration", 
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        # Capital initial
        ctk.CTkLabel(config_frame, text="Capital Initial ($)").pack(anchor="w", padx=10)
        capital_entry = ctk.CTkEntry(config_frame, textvariable=self.capital)
        capital_entry.pack(fill="x", padx=10, pady=2)
        
        # Perte max quotidienne
        ctk.CTkLabel(config_frame, text="Perte Max Quotidienne ($)").pack(anchor="w", padx=10)
        loss_entry = ctk.CTkEntry(config_frame, textvariable=self.max_loss)
        loss_entry.pack(fill="x", padx=10, pady=2)
        
        # Taille de position
        ctk.CTkLabel(config_frame, text="Taille Position Max (%)").pack(anchor="w", padx=10)
        position_entry = ctk.CTkEntry(config_frame, textvariable=self.position_size)
        position_entry.pack(fill="x", padx=10, pady=(2, 10))
        
        # Bouton sauvegarder config
        save_config_btn = ctk.CTkButton(
            config_frame,
            text="💾 Sauvegarder Config",
            command=self.save_config,
            height=35
        )
        save_config_btn.pack(fill="x", padx=10, pady=(0, 10))
    
    def create_monitoring_panel(self, parent):
        """Panneau de monitoring moderne"""
        
        # Titre
        ctk.CTkLabel(
            parent, 
            text="📊 MONITORING EN TEMPS RÉEL", 
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=(20, 10))
        
        # === MÉTRIQUES PRINCIPALES ===
        metrics_frame = ctk.CTkFrame(parent)
        metrics_frame.pack(fill="x", padx=20, pady=10)
        
        # Grille 2x2 pour les métriques
        metrics_grid = ctk.CTkFrame(metrics_frame)
        metrics_grid.pack(fill="x", padx=10, pady=10)
        
        # Portfolio Value
        portfolio_frame = ctk.CTkFrame(metrics_grid)
        portfolio_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(portfolio_frame, text="💼 Portfolio", font=ctk.CTkFont(size=12)).pack()
        self.portfolio_label = ctk.CTkLabel(
            portfolio_frame, 
            textvariable=self.portfolio_value, 
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="lightblue"
        )
        self.portfolio_label.pack()
        
        # Daily P&L
        pnl_frame = ctk.CTkFrame(metrics_grid)
        pnl_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(pnl_frame, text="📈 P&L Quotidien", font=ctk.CTkFont(size=12)).pack()
        self.pnl_label = ctk.CTkLabel(
            pnl_frame, 
            textvariable=self.daily_pnl, 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.pnl_label.pack()
        
        # Total Trades
        trades_frame = ctk.CTkFrame(metrics_grid)
        trades_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(trades_frame, text="🔄 Trades Total", font=ctk.CTkFont(size=12)).pack()
        ctk.CTkLabel(
            trades_frame, 
            textvariable=self.total_trades, 
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="orange"
        ).pack()
        
        # Win Rate
        winrate_frame = ctk.CTkFrame(metrics_grid)
        winrate_frame.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(winrate_frame, text="🎯 Win Rate", font=ctk.CTkFont(size=12)).pack()
        ctk.CTkLabel(
            winrate_frame, 
            textvariable=self.win_rate, 
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="lightgreen"
        ).pack()
        
        # Configuration de la grille
        metrics_grid.grid_columnconfigure(0, weight=1)
        metrics_grid.grid_columnconfigure(1, weight=1)
        
        # === LOG EN TEMPS RÉEL ===
        log_frame = ctk.CTkFrame(parent)
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(
            log_frame, 
            text="📝 Logs en Temps Réel", 
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        # Zone de texte pour les logs
        self.log_text = ctk.CTkTextbox(
            log_frame,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word"
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Bouton clear logs
        clear_btn = ctk.CTkButton(
            log_frame,
            text="🗑️ Effacer Logs",
            command=self.clear_logs,
            height=30
        )
        clear_btn.pack(pady=(0, 10))
    
    def setup_trading_engine(self):
        """Configuration du moteur de trading"""
        try:
            # Initialiser la base de données
            init_db()
            
            # Créer la stratégie RSI
            rsi_strategy = create_rsi_strategy(
                symbols=config.TRADING_PAIRS,
                timeframe=config.PRIMARY_TIMEFRAME
            )
            
            # Ajouter au moteur
            trading_engine.add_strategy(rsi_strategy)
            
            self.log_message("✅ Moteur de trading initialisé")
            
        except Exception as e:
            self.log_message(f"❌ Erreur initialisation: {e}")
    
    def toggle_trading_mode(self):
        """Basculer entre live et paper trading"""
        if self.is_live_trading.get():
            # Confirmation pour live trading
            result = messagebox.askyesno(
                "⚠️ CONFIRMATION LIVE TRADING",
                "ATTENTION: Vous allez activer le LIVE TRADING!\n\n"
                "Cela signifie que de l'ARGENT RÉEL sera utilisé.\n"
                "Êtes-vous sûr de vouloir continuer?",
                icon="warning"
            )
            
            if result:
                config.LIVE_TRADING = True
                config.PAPER_TRADING = False
                self.warning_label.pack(pady=5)
                self.log_message("⚠️ LIVE TRADING ACTIVÉ - ARGENT RÉEL!")
            else:
                self.is_live_trading.set(False)
        else:
            config.LIVE_TRADING = False
            config.PAPER_TRADING = True
            self.warning_label.pack_forget()
            self.log_message("📝 Paper Trading activé - Mode sécurisé")
    
    def toggle_bot(self):
        """Démarrer/Arrêter le bot"""
        if not self.is_running:
            self.start_bot()
        else:
            self.stop_bot()
    
    def start_bot(self):
        """Démarrer le bot de trading"""
        try:
            # Validation avant démarrage
            if self.is_live_trading.get():
                if not config.BINANCE_API_KEY or not config.BINANCE_SECRET_KEY:
                    messagebox.showerror(
                        "Erreur Configuration",
                        "Clés API Binance manquantes pour le live trading!"
                    )
                    return
            
            # Démarrer le moteur
            trading_engine.start()
            
            self.is_running = True
            self.start_button.configure(
                text="🛑 ARRÊTER",
                fg_color="red",
                hover_color="darkred"
            )
            self.pause_button.configure(state="normal")
            self.status_text.set("🟢 En Fonctionnement")
            
            mode = "LIVE" if self.is_live_trading.get() else "PAPER"
            self.log_message(f"🚀 Bot démarré en mode {mode}")
            
        except Exception as e:
            self.log_message(f"❌ Erreur démarrage: {e}")
            messagebox.showerror("Erreur", f"Impossible de démarrer le bot:\n{e}")
    
    def stop_bot(self):
        """Arrêter le bot"""
        try:
            trading_engine.stop()
            
            self.is_running = False
            self.start_button.configure(
                text="🚀 DÉMARRER",
                fg_color="green",
                hover_color="darkgreen"
            )
            self.pause_button.configure(state="disabled")
            self.status_text.set("🔴 Arrêté")
            
            self.log_message("🛑 Bot arrêté")
            
        except Exception as e:
            self.log_message(f"❌ Erreur arrêt: {e}")
    
    def pause_bot(self):
        """Mettre en pause/reprendre"""
        try:
            status = trading_engine.get_status()
            
            if status['is_paused']:
                trading_engine.resume()
                self.pause_button.configure(text="⏸️ PAUSE")
                self.status_text.set("🟢 En Fonctionnement")
                self.log_message("▶️ Bot repris")
            else:
                trading_engine.pause()
                self.pause_button.configure(text="▶️ REPRENDRE")
                self.status_text.set("⏸️ En Pause")
                self.log_message("⏸️ Bot en pause")
                
        except Exception as e:
            self.log_message(f"❌ Erreur pause: {e}")
    
    def emergency_stop(self):
        """Arrêt d'urgence"""
        result = messagebox.askyesno(
            "🛑 ARRÊT D'URGENCE",
            "Voulez-vous vraiment effectuer un arrêt d'urgence?\n\n"
            "Cela fermera toutes les positions et arrêtera le bot immédiatement.",
            icon="warning"
        )
        
        if result:
            try:
                trading_engine.stop()
                self.stop_bot()
                self.log_message("🛑 ARRÊT D'URGENCE EFFECTUÉ!")
                messagebox.showinfo("Arrêt d'Urgence", "Bot arrêté avec succès!")
            except Exception as e:
                self.log_message(f"❌ Erreur arrêt d'urgence: {e}")
    
    def save_config(self):
        """Sauvegarder la configuration"""
        try:
            config.INITIAL_CAPITAL = float(self.capital.get())
            config.MAX_DAILY_LOSS = float(self.max_loss.get())
            config.MAX_POSITION_SIZE = float(self.position_size.get()) / 100
            
            self.log_message("💾 Configuration sauvegardée")
            messagebox.showinfo("Configuration", "Configuration sauvegardée avec succès!")
            
        except ValueError:
            messagebox.showerror("Erreur", "Valeurs de configuration invalides!")
        except Exception as e:
            self.log_message(f"❌ Erreur sauvegarde: {e}")
    
    def update_metrics(self):
        """Mettre à jour les métriques"""
        try:
            if self.is_running:
                status = trading_engine.get_status()
                
                # Mettre à jour les valeurs
                self.portfolio_value.set(f"${status.get('portfolio_value', 0):,.2f}")
                
                pnl = status.get('current_pnl', 0)
                pnl_color = "lightgreen" if pnl >= 0 else "red"
                self.daily_pnl.set(f"${pnl:,.2f}")
                self.pnl_label.configure(text_color=pnl_color)
                
                self.total_trades.set(str(status.get('total_trades', 0)))
                
                # Calculer win rate
                total = status.get('total_trades', 0)
                successful = status.get('successful_trades', 0)
                win_rate = (successful / max(total, 1)) * 100
                self.win_rate.set(f"{win_rate:.1f}%")
                
        except Exception as e:
            self.log_message(f"❌ Erreur mise à jour métriques: {e}")
    
    def log_message(self, message):
        """Ajouter un message au log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        
        # Limiter le nombre de lignes
        lines = self.log_text.get("1.0", "end").split("\n")
        if len(lines) > 100:
            self.log_text.delete("1.0", "10.0")
    
    def clear_logs(self):
        """Effacer les logs"""
        self.log_text.delete("1.0", "end")
        self.log_message("🗑️ Logs effacés")
    
    def start_update_loop(self):
        """Démarrer la boucle de mise à jour"""
        def update_loop():
            while True:
                try:
                    self.root.after(0, self.update_metrics)
                    time.sleep(2)  # Mise à jour toutes les 2 secondes
                except:
                    break
        
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def on_closing(self):
        """Gestionnaire de fermeture"""
        if self.is_running:
            result = messagebox.askyesno(
                "Fermeture",
                "Le bot est en cours d'exécution.\n"
                "Voulez-vous l'arrêter et fermer l'application?"
            )
            if result:
                self.stop_bot()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """Lancer l'application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("🚀 INVESTT Trading Bot démarré")
        self.log_message("💡 Configurez vos paramètres et cliquez sur DÉMARRER")
        self.root.mainloop()


def main():
    """Point d'entrée principal"""
    try:
        app = ModernTradingApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur Critique", f"Erreur lors du démarrage:\n{e}")


if __name__ == "__main__":
    main()
