"""
🤖 INVESTT AI Lite Trading Bot - Build Script Optimisé
Créer l'EXE sans pandas/numpy pour éviter les conflits
"""
import subprocess
import sys
import os
from pathlib import Path
import shutil

def print_header():
    """Afficher l'en-tête"""
    print("🤖 BUILD INVESTT AI LITE TRADING BOT")
    print("=" * 50)
    print("Version optimisée sans pandas/numpy")
    print("=" * 50 + "\n")

def install_lite_dependencies():
    """Installer seulement les dépendances essentielles"""
    print("📦 Installation des dépendances Lite...")
    
    # Dépendances minimales (sans pandas/numpy)
    lite_deps = [
        "customtkinter>=5.2.0",
        "python-dotenv>=1.0.0",
        "pillow>=10.0.0",
        "pyinstaller>=5.13.0"
    ]
    
    success_count = 0
    
    for i, dep in enumerate(lite_deps, 1):
        try:
            print(f"   [{i}/{len(lite_deps)}] {dep.split('>=')[0]}...", end=" ")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dep],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅")
                success_count += 1
            else:
                print("❌")
                print(f"      Erreur: {result.stderr[:100]}...")
                
        except Exception as e:
            print(f"❌ {e}")
    
    print(f"\n📊 Installation terminée: {success_count}/{len(lite_deps)} paquets")
    return success_count >= len(lite_deps) - 1  # Au moins 3/4

def create_lite_spec():
    """Créer le fichier .spec optimisé pour Lite"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['investt_ai_lite.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('.env.example', '.'),
    ],
    hiddenimports=[
        'customtkinter',
        'tkinter',
        'PIL',
        'dotenv',
        'threading',
        'pathlib',
        'math',
        'random',
        'time',
        'datetime'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        # Exclure explicitement les dépendances lourdes
        'pandas',
        'numpy',
        'scipy',
        'matplotlib',
        'seaborn',
        'plotly',
        'jupyter',
        'notebook',
        'IPython',
        'sklearn',
        'tensorflow',
        'torch',
        'keras'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='INVESTT_AI_Lite_Trading_Bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("ai_lite.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ Fichier .spec Lite créé")

def build_lite_exe():
    """Construire l'EXE Lite"""
    print("\n🔨 Construction de l'EXE IA Lite...")
    print("⏳ Cela devrait prendre 2-5 minutes (beaucoup plus rapide!)...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "PyInstaller", "--clean", "ai_lite.spec"],
            capture_output=True,
            text=True,
            timeout=600  # 10 minutes max
        )
        
        if result.returncode == 0:
            print("✅ EXE IA Lite créé avec succès!")
            
            exe_path = Path("dist/INVESTT_AI_Lite_Trading_Bot.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Fichier: {exe_path}")
                print(f"📊 Taille: {size_mb:.1f} MB (beaucoup plus léger!)")
                return True
            else:
                print("❌ Fichier EXE non trouvé")
                return False
        else:
            print(f"❌ Erreur PyInstaller:")
            # Afficher seulement les erreurs importantes
            stderr_lines = result.stderr.split('\n')
            for line in stderr_lines[-15:]:  # Dernières 15 lignes
                if any(keyword in line for keyword in ['ERROR', 'CRITICAL', 'ModuleNotFoundError', 'ImportError']):
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout - Le build a pris trop de temps")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_lite_package():
    """Créer le package IA Lite final"""
    print("\n📦 Création du package IA Lite...")
    
    try:
        # Créer le dossier final
        lite_folder = Path("INVESTT_AI_Lite_Final")
        if lite_folder.exists():
            shutil.rmtree(lite_folder)
        
        lite_folder.mkdir()
        
        # Copier l'EXE
        exe_source = Path("dist/INVESTT_AI_Lite_Trading_Bot.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, lite_folder / "INVESTT_AI_Lite_Trading_Bot.exe")
            print("✅ EXE IA Lite copié")
        
        # Créer un fichier .env optimisé
        env_content = '''# INVESTT AI Lite Trading Bot - Configuration
# Version légère sans pandas/numpy

BINANCE_API_KEY=your_api_key_here
BINANCE_SECRET_KEY=your_secret_key_here
BINANCE_TESTNET=true

# Paramètres de Trading
INITIAL_CAPITAL=1000.0
MAX_POSITION_SIZE=0.02
MAX_DAILY_LOSS=150.0

# Paramètres IA Lite
AI_ENABLED=true
ML_WEIGHT=50
TECHNICAL_WEIGHT=50
SENTIMENT_WEIGHT=20

# Mode
PAPER_TRADING=true
LIVE_TRADING=false
'''
        
        with open(lite_folder / ".env", "w") as f:
            f.write(env_content)
        print("✅ Configuration IA Lite créée")
        
        # Créer un guide IA Lite
        lite_guide = '''🤖 INVESTT AI LITE TRADING BOT - GUIDE RAPIDE

═══════════════════════════════════════════════════════════════

🚀 IA LITE - VERSION OPTIMISÉE:
   • Analyse technique simplifiée (RSI, MACD)
   • Prédictions ML légères (sans pandas/numpy)
   • Détection de patterns basique
   • Analyse de sentiment simulée
   • Apprentissage adaptatif intégré
   • Build ultra-rapide et léger

🎯 DÉMARRAGE IMMÉDIAT:
   1. Double-cliquez sur "INVESTT_AI_Lite_Trading_Bot.exe"
   2. L'interface IA Lite s'ouvre instantanément
   3. Ajustez les poids ML/Technique/Sentiment
   4. Cliquez sur "DÉMARRER IA LITE" pour la démo

⚙️ CONFIGURATION IA LITE:
   1. Poids ML: Influence des prédictions légères (0-100%)
   2. Poids Technique: Influence de l'analyse RSI/MACD (0-100%)
   3. Poids Sentiment: Influence du sentiment simulé (0-50%)
   4. Les poids s'adaptent automatiquement

🧠 MODULES IA LITE INCLUS:

   📊 ANALYSE TECHNIQUE SIMPLIFIÉE:
   - RSI (Relative Strength Index)
   - MACD (Moving Average Convergence Divergence)
   - Détection de tendances
   - Support/résistance basique

   🤖 ML LÉGER:
   - Prédictions sans dépendances lourdes
   - Modèle simplifié mais efficace
   - Apprentissage adaptatif
   - Confiance calculée en temps réel

   🔍 PATTERNS BASIQUES:
   - Tendances haussières/baissières
   - Doji simplifiés
   - Proximité support/résistance
   - Détection d'anomalies

   📰 SENTIMENT SIMULÉ:
   - Simulation réaliste du sentiment
   - Cycles de marché intégrés
   - Facteurs de volatilité
   - Classification automatique

   🎯 APPRENTISSAGE ADAPTATIF:
   - Apprend des trades passés
   - Adapte les seuils RSI
   - Optimise les poids automatiquement
   - Sauvegarde les paramètres

🔧 LIVE TRADING IA LITE:
   1. Obtenez vos clés API sur binance.com
   2. Saisissez-les dans l'interface
   3. Testez la connexion
   4. Basculez le switch "LIVE TRADING"
   5. L'IA Lite trade avec de l'argent réel!

🛡️ SÉCURITÉ:
   ⚠️ Version allégée mais sécurisée
   ⚠️ Testez d'abord en Paper Trading
   ⚠️ Surveillez les premières heures
   ⚠️ Arrêt d'urgence toujours disponible

📊 INTERFACE IA LITE:
   - Panneau de contrôle simplifié
   - Monitoring temps réel optimisé
   - Analytics IA essentielles
   - Logs détaillés des décisions
   - Actions IA: optimisation, analyse, reset

💡 AVANTAGES IA LITE:
   ✅ Build ultra-rapide (2-5 min vs 10+ min)
   ✅ EXE compact (20-40 MB vs 200+ MB)
   ✅ Zéro problème de dépendances
   ✅ Interface identique et moderne
   ✅ IA fonctionnelle et adaptative
   ✅ Compatible tous PC Windows

🔬 FONCTIONNALITÉS:
   - Analyse en temps réel
   - Prédictions ML simplifiées
   - Apprentissage continu
   - Adaptation automatique
   - Interface moderne
   - Logs complets

═══════════════════════════════════════════════════════════════
🤖 IA LITE PRÊTE POUR LE TRADING INTELLIGENT ! 🚀💰
═══════════════════════════════════════════════════════════════
'''
        
        with open(lite_folder / "GUIDE_IA_LITE.txt", "w", encoding='utf-8') as f:
            f.write(lite_guide)
        print("✅ Guide IA Lite créé")
        
        # Créer un lanceur IA Lite
        lite_launcher = '''@echo off
title INVESTT AI Lite Trading Bot
color 0A
echo.
echo ████████████████████████████████████████████████████████████
echo █                                                          █
echo █    🤖 INVESTT AI LITE - VERSION OPTIMISÉE              █
echo █                                                          █
echo ████████████████████████████████████████████████████████████
echo.
echo 🧠 Lancement de l'IA Lite...
echo 📊 Version légère sans pandas/numpy
echo ⚡ Build ultra-rapide et stable
echo.
start "" "INVESTT_AI_Lite_Trading_Bot.exe"
echo ✅ IA Lite lancée avec succès!
echo.
echo 💡 Interface IA Lite optimisée
echo 🔬 Tous les modules essentiels inclus
echo.
timeout /t 2 >nul
'''
        
        with open(lite_folder / "Lancer_IA_Lite.bat", "w") as f:
            f.write(lite_launcher)
        print("✅ Lanceur IA Lite créé")
        
        print(f"\n✅ Package IA Lite complet créé: {lite_folder}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création package IA Lite: {e}")
        return False

def main():
    """Fonction principale"""
    print_header()
    
    # Vérifier que le fichier source existe
    if not Path("investt_ai_lite.py").exists():
        print("❌ Fichier investt_ai_lite.py non trouvé!")
        print("💡 Assurez-vous d'être dans le bon dossier")
        input("Appuyez sur Entrée pour fermer...")
        return
    
    # Étapes de build IA Lite
    steps = [
        ("Installation dépendances Lite", install_lite_dependencies),
        ("Création fichier .spec Lite", create_lite_spec),
        ("Construction EXE IA Lite", build_lite_exe),
        ("Création package IA Lite", create_lite_package)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"🔄 {step_name}...")
        try:
            if step_func in [install_lite_dependencies, build_lite_exe, create_lite_package]:
                success = step_func()
                if success:
                    success_count += 1
                elif step_func == build_lite_exe:
                    print("💡 Essayez de relancer si l'erreur persiste")
                    break
            else:
                step_func()
                success_count += 1
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    # Résumé final
    print("\n" + "=" * 60)
    print("🤖 BUILD IA LITE TERMINÉ!")
    print("=" * 60)
    
    final_exe = Path("INVESTT_AI_Lite_Final/INVESTT_AI_Lite_Trading_Bot.exe")
    if final_exe.exists():
        print("✅ IA LITE CRÉÉE AVEC SUCCÈS!")
        print(f"\n📁 Dossier: INVESTT_AI_Lite_Final/")
        print(f"🤖 Fichier: INVESTT_AI_Lite_Trading_Bot.exe")
        print(f"📊 Taille: {final_exe.stat().st_size / (1024*1024):.1f} MB")
        
        print("\n🧠 MODULES IA LITE INCLUS:")
        print("   ✅ Analyse technique simplifiée")
        print("   ✅ ML léger sans pandas/numpy")
        print("   ✅ Détection de patterns basique")
        print("   ✅ Sentiment simulé réaliste")
        print("   ✅ Apprentissage adaptatif")
        
        print("\n🚀 UTILISATION:")
        print("   1. Allez dans 'INVESTT_AI_Lite_Final'")
        print("   2. Double-cliquez sur 'Lancer_IA_Lite.bat'")
        print("   3. Interface IA Lite moderne")
        print("   4. Ajustez les paramètres selon vos besoins")
        print("   5. Démarrez l'IA Lite!")
        
        print("\n💡 AVANTAGES IA LITE:")
        print("   ⚡ Build ultra-rapide (2-5 min)")
        print("   📦 EXE compact (20-40 MB)")
        print("   🛡️ Zéro problème de dépendances")
        print("   🤖 IA fonctionnelle et adaptative")
        print("   🖥️ Compatible tous PC Windows")
        print("   💰 Prêt pour le live trading")
        
    else:
        print("⚠️ EXE IA Lite non créé")
        print(f"✅ Étapes réussies: {success_count}/{len(steps)}")
        print("\n💡 CONSEILS DE DÉPANNAGE:")
        print("   • Vérifiez que Python est bien installé")
        print("   • Essayez de relancer le script")
        print("   • Vérifiez les permissions d'écriture")
        print("   • Fermez l'antivirus temporairement")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Build IA Lite interrompu")
    except Exception as e:
        print(f"\n\n❌ Erreur critique: {e}")
    finally:
        input("\nAppuyez sur Entrée pour fermer...")
