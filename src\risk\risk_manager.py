"""
Risk management system for trading operations
"""
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
import pandas as pd
import numpy as np

from config import config, RISK_PARAMS
from src.utils.logger import log_risk, log_error
from src.database import session_scope, Trade, Position, PerformanceMetrics
from src.strategies import Signal


@dataclass
class RiskCheck:
    """Risk check result"""
    passed: bool
    reason: str
    severity: str  # 'low', 'medium', 'high', 'critical'
    recommended_action: str
    details: Dict = None


@dataclass
class PositionSize:
    """Position sizing calculation result"""
    amount: float
    value: float
    risk_percentage: float
    max_loss: float
    reasoning: str


class RiskManager:
    """Comprehensive risk management system"""
    
    def __init__(self):
        self.max_daily_loss = RISK_PARAMS['max_daily_loss']
        self.max_position_size = RISK_PARAMS['max_position_size']
        self.max_drawdown = RISK_PARAMS['max_drawdown']
        self.correlation_limit = RISK_PARAMS['correlation_limit']
        self.volatility_filter = RISK_PARAMS['volatility_filter']
        self.max_concurrent_trades = RISK_PARAMS['max_concurrent_trades']
        
        # Risk state tracking
        self.daily_pnl = 0.0
        self.current_drawdown = 0.0
        self.active_positions = {}
        self.risk_alerts = []
        
        self.initialize_risk_state()
    
    def initialize_risk_state(self):
        """Initialize risk state from database"""
        try:
            with session_scope() as session:
                # Get today's performance
                today = datetime.utcnow().date()
                today_performance = session.query(PerformanceMetrics).filter(
                    PerformanceMetrics.date >= today
                ).first()
                
                if today_performance:
                    self.daily_pnl = today_performance.daily_pnl
                    self.current_drawdown = today_performance.max_drawdown
                
                # Get active positions
                active_positions = session.query(Position).all()
                for position in active_positions:
                    self.active_positions[position.symbol] = {
                        'side': position.side,
                        'amount': position.amount,
                        'entry_price': position.entry_price,
                        'current_price': position.current_price,
                        'unrealized_pnl': position.unrealized_pnl
                    }
                    
        except Exception as e:
            log_error("Failed to initialize risk state", e)
    
    def check_signal_risk(self, signal: Signal, current_portfolio_value: float) -> RiskCheck:
        """Comprehensive risk check for a trading signal"""
        checks = []
        
        # 1. Daily loss limit check
        checks.append(self._check_daily_loss_limit())
        
        # 2. Position concentration check
        checks.append(self._check_position_concentration(signal.symbol))
        
        # 3. Maximum concurrent trades check
        checks.append(self._check_concurrent_trades())
        
        # 4. Drawdown check
        checks.append(self._check_drawdown_limit())
        
        # 5. Volatility check
        checks.append(self._check_volatility(signal.symbol))
        
        # 6. Correlation check
        checks.append(self._check_correlation(signal.symbol))
        
        # 7. Market hours check
        checks.append(self._check_market_hours())
        
        # Evaluate overall risk
        failed_checks = [check for check in checks if not check.passed]
        
        if not failed_checks:
            return RiskCheck(
                passed=True,
                reason="All risk checks passed",
                severity="low",
                recommended_action="proceed",
                details={'checks': len(checks), 'passed': len(checks)}
            )
        
        # Determine severity based on failed checks
        critical_failures = [c for c in failed_checks if c.severity == 'critical']
        high_failures = [c for c in failed_checks if c.severity == 'high']
        
        if critical_failures:
            severity = 'critical'
            action = 'reject'
        elif high_failures:
            severity = 'high'
            action = 'reduce_size'
        else:
            severity = 'medium'
            action = 'proceed_with_caution'
        
        return RiskCheck(
            passed=len(critical_failures) == 0,
            reason=f"Failed {len(failed_checks)} risk checks",
            severity=severity,
            recommended_action=action,
            details={
                'failed_checks': [c.reason for c in failed_checks],
                'critical_failures': len(critical_failures),
                'high_failures': len(high_failures)
            }
        )
    
    def calculate_position_size(self, signal: Signal, current_portfolio_value: float, 
                              current_price: float) -> PositionSize:
        """Calculate optimal position size based on risk parameters"""
        
        # Base position size (percentage of portfolio)
        base_risk_pct = self.max_position_size
        
        # Adjust based on signal strength
        signal_multiplier = signal.strength
        
        # Adjust based on volatility
        volatility_multiplier = self._get_volatility_multiplier(signal.symbol)
        
        # Adjust based on current drawdown
        drawdown_multiplier = max(0.1, 1.0 - (self.current_drawdown / self.max_drawdown))
        
        # Calculate final risk percentage
        final_risk_pct = base_risk_pct * signal_multiplier * volatility_multiplier * drawdown_multiplier
        
        # Calculate position value
        position_value = current_portfolio_value * final_risk_pct
        
        # Calculate amount based on current price
        amount = position_value / current_price
        
        # Calculate maximum potential loss (assuming 2% stop loss)
        max_loss = position_value * config.STOP_LOSS_PERCENTAGE
        
        reasoning = (
            f"Base risk: {base_risk_pct:.1%}, "
            f"Signal strength: {signal_multiplier:.2f}, "
            f"Volatility adj: {volatility_multiplier:.2f}, "
            f"Drawdown adj: {drawdown_multiplier:.2f}"
        )
        
        return PositionSize(
            amount=amount,
            value=position_value,
            risk_percentage=final_risk_pct,
            max_loss=max_loss,
            reasoning=reasoning
        )
    
    def _check_daily_loss_limit(self) -> RiskCheck:
        """Check if daily loss limit is exceeded"""
        if self.daily_pnl <= -self.max_daily_loss:
            return RiskCheck(
                passed=False,
                reason=f"Daily loss limit exceeded: {self.daily_pnl:.2f}",
                severity="critical",
                recommended_action="stop_trading"
            )
        
        # Warning if approaching limit
        if self.daily_pnl <= -self.max_daily_loss * 0.8:
            return RiskCheck(
                passed=True,
                reason=f"Approaching daily loss limit: {self.daily_pnl:.2f}",
                severity="high",
                recommended_action="reduce_size"
            )
        
        return RiskCheck(
            passed=True,
            reason="Daily loss within limits",
            severity="low",
            recommended_action="proceed"
        )
    
    def _check_position_concentration(self, symbol: str) -> RiskCheck:
        """Check position concentration risk"""
        if symbol in self.active_positions:
            return RiskCheck(
                passed=False,
                reason=f"Already have position in {symbol}",
                severity="medium",
                recommended_action="skip"
            )
        
        return RiskCheck(
            passed=True,
            reason="No existing position in symbol",
            severity="low",
            recommended_action="proceed"
        )
    
    def _check_concurrent_trades(self) -> RiskCheck:
        """Check maximum concurrent trades limit"""
        active_count = len(self.active_positions)
        
        if active_count >= self.max_concurrent_trades:
            return RiskCheck(
                passed=False,
                reason=f"Maximum concurrent trades reached: {active_count}",
                severity="high",
                recommended_action="wait"
            )
        
        return RiskCheck(
            passed=True,
            reason=f"Concurrent trades within limit: {active_count}/{self.max_concurrent_trades}",
            severity="low",
            recommended_action="proceed"
        )
    
    def _check_drawdown_limit(self) -> RiskCheck:
        """Check maximum drawdown limit"""
        if self.current_drawdown >= self.max_drawdown:
            return RiskCheck(
                passed=False,
                reason=f"Maximum drawdown exceeded: {self.current_drawdown:.1%}",
                severity="critical",
                recommended_action="stop_trading"
            )
        
        # Warning if approaching limit
        if self.current_drawdown >= self.max_drawdown * 0.8:
            return RiskCheck(
                passed=True,
                reason=f"Approaching drawdown limit: {self.current_drawdown:.1%}",
                severity="high",
                recommended_action="reduce_size"
            )
        
        return RiskCheck(
            passed=True,
            reason="Drawdown within limits",
            severity="low",
            recommended_action="proceed"
        )
    
    def _check_volatility(self, symbol: str) -> RiskCheck:
        """Check market volatility"""
        if not self.volatility_filter:
            return RiskCheck(
                passed=True,
                reason="Volatility filter disabled",
                severity="low",
                recommended_action="proceed"
            )
        
        # This would need historical data to calculate volatility
        # For now, return a basic check
        return RiskCheck(
            passed=True,
            reason="Volatility check passed",
            severity="low",
            recommended_action="proceed"
        )
    
    def _check_correlation(self, symbol: str) -> RiskCheck:
        """Check correlation with existing positions"""
        # This would need correlation analysis between symbols
        # For now, return a basic check
        return RiskCheck(
            passed=True,
            reason="Correlation check passed",
            severity="low",
            recommended_action="proceed"
        )
    
    def _check_market_hours(self) -> RiskCheck:
        """Check if markets are open"""
        # For crypto, markets are always open
        # For traditional markets, would need to check trading hours
        return RiskCheck(
            passed=True,
            reason="Markets are open",
            severity="low",
            recommended_action="proceed"
        )
    
    def _get_volatility_multiplier(self, symbol: str) -> float:
        """Get volatility-based position size multiplier"""
        # This would calculate based on historical volatility
        # For now, return a default multiplier
        return 1.0
    
    def update_position(self, symbol: str, side: str, amount: float, entry_price: float):
        """Update active position tracking"""
        self.active_positions[symbol] = {
            'side': side,
            'amount': amount,
            'entry_price': entry_price,
            'current_price': entry_price,
            'unrealized_pnl': 0.0
        }
        
        log_risk(f"Position updated: {side} {amount} {symbol} at {entry_price}")
    
    def close_position(self, symbol: str, exit_price: float, realized_pnl: float):
        """Close position and update risk state"""
        if symbol in self.active_positions:
            del self.active_positions[symbol]
            self.daily_pnl += realized_pnl
            
            log_risk(f"Position closed: {symbol} at {exit_price}, PnL: {realized_pnl:.2f}")
    
    def update_daily_pnl(self, pnl_change: float):
        """Update daily P&L"""
        self.daily_pnl += pnl_change
    
    def get_risk_status(self) -> Dict:
        """Get current risk status"""
        return {
            'daily_pnl': self.daily_pnl,
            'daily_loss_limit': self.max_daily_loss,
            'current_drawdown': self.current_drawdown,
            'max_drawdown': self.max_drawdown,
            'active_positions': len(self.active_positions),
            'max_concurrent_trades': self.max_concurrent_trades,
            'risk_alerts': len(self.risk_alerts),
            'trading_allowed': self.daily_pnl > -self.max_daily_loss and self.current_drawdown < self.max_drawdown
        }


# Global risk manager instance
risk_manager = RiskManager()


def get_risk_manager() -> RiskManager:
    """Get the global risk manager"""
    return risk_manager
