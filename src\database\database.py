"""
Database connection and session management
"""
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import St<PERSON>Pool
from contextlib import contextmanager
from typing import Generator
import os

from config import config
from .models import Base


class DatabaseManager:
    """Database connection and session manager"""
    
    def __init__(self):
        self.engine = None
        self.SessionLocal = None
        self.initialize_database()
    
    def initialize_database(self):
        """Initialize database connection and create tables"""
        # Create engine based on database URL
        if config.DATABASE_URL.startswith("sqlite"):
            self.engine = create_engine(
                config.DATABASE_URL,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
                echo=False  # Set to True for SQL debugging
            )
        else:
            self.engine = create_engine(
                config.DATABASE_URL,
                pool_pre_ping=True,
                echo=False
            )
        
        # Create session factory
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
        
        # Create all tables
        self.create_tables()
    
    def create_tables(self):
        """Create all database tables"""
        Base.metadata.create_all(bind=self.engine)
    
    def drop_tables(self):
        """Drop all database tables (use with caution!)"""
        Base.metadata.drop_all(bind=self.engine)
    
    def get_session(self) -> Session:
        """Get a new database session"""
        return self.SessionLocal()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """Provide a transactional scope around a series of operations"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
    
    def close(self):
        """Close database connections"""
        if self.engine:
            self.engine.dispose()


# Global database manager instance
db_manager = DatabaseManager()


def get_db() -> Generator[Session, None, None]:
    """Dependency to get database session"""
    with db_manager.session_scope() as session:
        yield session


def get_session() -> Session:
    """Get a new database session (remember to close it!)"""
    return db_manager.get_session()


@contextmanager
def session_scope() -> Generator[Session, None, None]:
    """Context manager for database sessions"""
    with db_manager.session_scope() as session:
        yield session


# Database utility functions
def init_db():
    """Initialize database (create tables)"""
    db_manager.create_tables()


def reset_db():
    """Reset database (drop and recreate tables)"""
    db_manager.drop_tables()
    db_manager.create_tables()


def close_db():
    """Close database connections"""
    db_manager.close()


__all__ = [
    'DatabaseManager', 'db_manager', 'get_db', 'get_session', 
    'session_scope', 'init_db', 'reset_db', 'close_db'
]
