# 💰 Guide Live Trading INVESTT

## 🚀 Passage au Live Trading

### ⚠️ AVERTISSEMENT IMPORTANT
**LE LIVE TRADING UTILISE DE L'ARGENT RÉEL !**
- Commencez avec de petits montants
- Ne risquez jamais plus que vous pouvez perdre
- Testez d'abord en paper trading
- Surveillez constamment les performances

## 🔧 Configuration pour Live Trading

### 1. Obtenir les Clés API Binance

1. **Créer un compte Binance**
   - Allez sur [binance.com](https://binance.com)
   - Créez votre compte et vérifiez votre identité

2. **Générer les clés API**
   - Connectez-vous à Binance
   - Allez dans "Profil" > "Sécurité API"
   - Cliquez sur "Créer une API"
   - Nom: "INVESTT Trading Bot"
   - **Permissions importantes:**
     - ✅ Lecture des informations du compte
     - ✅ Trading au comptant
     - ❌ Retrait (JAMAIS activer)
     - ❌ Trading sur marge
     - ❌ Trading de futures

3. **Restrictions IP (Recommandé)**
   - Ajoutez votre IP publique pour plus de sécurité
   - Trouvez votre IP: [whatismyipaddress.com](https://whatismyipaddress.com)

### 2. Configuration du Fichier .env

Éditez le fichier `.env` avec vos vraies clés :

```env
# API Configuration - VOS VRAIES CLÉS
BINANCE_API_KEY=votre_vraie_cle_api_ici
BINANCE_SECRET_KEY=votre_vraie_cle_secrete_ici
BINANCE_TESTNET=false

# Trading Mode - LIVE TRADING ACTIVÉ
PAPER_TRADING=false
LIVE_TRADING=true

# Capital - COMMENCEZ PETIT !
INITIAL_CAPITAL=500.0

# Sécurité - LIMITES STRICTES
MAX_POSITION_SIZE=0.01    # 1% par trade (très conservateur)
MAX_DAILY_LOSS=50.0       # Perte max 50€ par jour
MAX_DRAWDOWN=0.05         # Drawdown max 5%

# Stratégie - PARAMÈTRES CONSERVATEURS
RSI_PERIOD=14
RSI_OVERSOLD=25.0         # Plus conservateur
RSI_OVERBOUGHT=75.0       # Plus conservateur

# Paires - COMMENCEZ AVEC UNE SEULE
TRADING_PAIRS=BTC/USDT

# Timeframe - PLUS LONG = PLUS SÛR
PRIMARY_TIMEFRAME=15m
```

### 3. Vérifications Avant Live Trading

#### ✅ Checklist de Sécurité

- [ ] **Clés API configurées** avec permissions limitées
- [ ] **Capital limité** sur le compte (max 1000€ pour débuter)
- [ ] **Limites de risque** très conservatrices
- [ ] **Une seule paire** pour commencer
- [ ] **Timeframe long** (15m ou 1h)
- [ ] **Tests en paper trading** réussis
- [ ] **Surveillance active** prévue

#### ✅ Checklist Technique

- [ ] **Application testée** en paper trading
- [ ] **Connexion internet stable**
- [ ] **Ordinateur dédié** (pas d'extinction)
- [ ] **Logs activés** pour surveillance
- [ ] **Sauvegardes** de la configuration
- [ ] **Plan d'arrêt d'urgence** défini

## 🎯 Stratégie de Démarrage Live

### Phase 1: Test Minimal (1-2 semaines)
```env
INITIAL_CAPITAL=100.0
MAX_POSITION_SIZE=0.005   # 0.5% par trade
MAX_DAILY_LOSS=10.0       # 10€ max par jour
TRADING_PAIRS=BTC/USDT    # Une seule paire
```

### Phase 2: Expansion Prudente (1 mois)
```env
INITIAL_CAPITAL=500.0
MAX_POSITION_SIZE=0.01    # 1% par trade
MAX_DAILY_LOSS=25.0       # 25€ max par jour
TRADING_PAIRS=BTC/USDT,ETH/USDT
```

### Phase 3: Opération Normale (après validation)
```env
INITIAL_CAPITAL=1000.0
MAX_POSITION_SIZE=0.02    # 2% par trade
MAX_DAILY_LOSS=50.0       # 50€ max par jour
TRADING_PAIRS=BTC/USDT,ETH/USDT,BNB/USDT
```

## 🚀 Lancement en Live Trading

### 1. Démarrage de l'Application

```bash
# Méthode 1: Raccourci bureau
Double-clic sur "INVESTT Trading Bot"

# Méthode 2: Script de lancement
Double-clic sur "launch_investt.bat"

# Méthode 3: Ligne de commande
python investt_app.py
```

### 2. Configuration dans l'Interface

1. **Vérifier le mode**
   - Basculer le switch "LIVE TRADING" sur ON
   - ⚠️ Message d'avertissement apparaît

2. **Confirmer les paramètres**
   - Capital initial
   - Limites de risque
   - Sauvegarder la configuration

3. **Démarrer le bot**
   - Cliquer sur "🚀 DÉMARRER"
   - Confirmer le live trading
   - Surveiller les logs

### 3. Surveillance Active

#### Métriques à Surveiller
- **Portfolio Value**: Valeur totale
- **Daily P&L**: Profit/Perte du jour
- **Total Trades**: Nombre de trades
- **Win Rate**: Taux de réussite

#### Signaux d'Alerte
- 🔴 **Perte quotidienne** > 50% de la limite
- 🔴 **Drawdown** > 3%
- 🔴 **Win rate** < 40%
- 🔴 **Erreurs** répétées dans les logs

## 🛡️ Gestion des Risques Live

### Règles d'Or

1. **JAMAIS dépasser les limites** définies
2. **Arrêter immédiatement** si perte > limite quotidienne
3. **Surveiller en permanence** les premières heures
4. **Garder un journal** des performances
5. **Réviser les paramètres** régulièrement

### Situations d'Arrêt Immédiat

- ❌ Perte quotidienne atteinte
- ❌ Comportement anormal du bot
- ❌ Erreurs de connexion répétées
- ❌ Mouvements de marché extrêmes
- ❌ Doute sur le fonctionnement

### Plan d'Urgence

1. **Arrêt d'urgence** dans l'interface
2. **Fermeture manuelle** des positions sur Binance
3. **Désactivation des clés API** si nécessaire
4. **Analyse des logs** pour comprendre
5. **Retour en paper trading** pour tests

## 📊 Optimisation Progressive

### Semaine 1-2: Observation
- Surveiller sans modifier
- Noter les performances
- Identifier les patterns

### Semaine 3-4: Ajustements Mineurs
- Ajuster RSI si nécessaire
- Modifier timeframe si besoin
- Optimiser les seuils

### Mois 2+: Expansion
- Ajouter des paires progressivement
- Augmenter le capital prudemment
- Tester de nouvelles stratégies

## 🔧 Maintenance et Monitoring

### Surveillance Quotidienne
- Vérifier les performances au réveil
- Contrôler les logs d'erreur
- Valider les positions ouvertes
- Surveiller les news du marché

### Surveillance Hebdomadaire
- Analyser les métriques de performance
- Réviser les paramètres de risque
- Sauvegarder les données
- Mettre à jour si nécessaire

### Surveillance Mensuelle
- Bilan complet des performances
- Optimisation des stratégies
- Révision des objectifs
- Planification des évolutions

## 📞 Support et Dépannage

### Problèmes Courants

1. **Erreur de connexion API**
   - Vérifier les clés API
   - Contrôler les permissions
   - Vérifier la connexion internet

2. **Ordres rejetés**
   - Vérifier le solde du compte
   - Contrôler les limites de trading
   - Vérifier les restrictions de paire

3. **Performance décevante**
   - Analyser les conditions de marché
   - Réviser les paramètres de stratégie
   - Considérer une pause temporaire

### Logs Importants

```bash
# Logs principaux
tail -f logs/investt.log

# Logs de trading
tail -f logs/trading.log

# Logs d'erreurs
tail -f logs/errors.log
```

## 🎯 Objectifs Réalistes

### Court Terme (1-3 mois)
- **Objectif**: Ne pas perdre d'argent
- **Rendement**: 0-2% par mois
- **Focus**: Apprentissage et stabilité

### Moyen Terme (3-12 mois)
- **Objectif**: Rentabilité constante
- **Rendement**: 2-5% par mois
- **Focus**: Optimisation et croissance

### Long Terme (1 an+)
- **Objectif**: Performance supérieure
- **Rendement**: 5-10% par mois
- **Focus**: Expansion et sophistication

---

## ⚠️ RAPPEL FINAL

**LE TRADING COMPORTE DES RISQUES DE PERTE**

- Commencez petit
- Apprenez constamment
- Ne risquez que ce que vous pouvez perdre
- Arrêtez si vous doutez
- Demandez de l'aide si nécessaire

**BONNE CHANCE DANS VOTRE AVENTURE DE TRADING AUTOMATISÉ ! 🚀💰**
