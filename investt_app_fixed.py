"""
🚀 INVESTT Trading Bot - Application Desktop Ultra-Moderne (Version Corrigée)
Interface graphique moderne avec CustomTkinter - Sans dépendances problématiques
"""
import customtkinter as ctk
import tkinter as tk
from tkinter import messagebox
import threading
import time
from datetime import datetime
from pathlib import Path
import sys
import os

# Configuration du thème moderne
ctk.set_appearance_mode("dark")
ctk.set_default_color_theme("blue")

# Ajouter le chemin src
sys.path.append(str(Path(__file__).parent))

# Import de la configuration simplifiée
try:
    from config_simple import config
except ImportError:
    # Fallback avec configuration basique
    class BasicConfig:
        INITIAL_CAPITAL = 1000.0
        MAX_DAILY_LOSS = 150.0
        MAX_POSITION_SIZE = 0.02
        PAPER_TRADING = True
        LIVE_TRADING = False
        TRADING_PAIRS = ["BTC/USDT", "ETH/USDT"]
        PRIMARY_TIMEFRAME = "5m"
        RSI_PERIOD = 14
        RSI_OVERSOLD = 30.0
        RSI_OVERBOUGHT = 70.0
        BINANCE_API_KEY = ""
        BINANCE_SECRET_KEY = ""
    
    config = BasicConfig()

class ModernTradingApp:
    """Application Desktop Moderne pour INVESTT Trading Bot"""
    
    def __init__(self):
        self.root = ctk.CTk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        self.start_update_loop()
        
        # État du bot simulé
        self.bot_running = False
        self.bot_paused = False
        self.trades_count = 0
        self.portfolio_value_num = config.INITIAL_CAPITAL
        self.daily_pnl_num = 0.0
    
    def setup_window(self):
        """Configuration de la fenêtre principale"""
        self.root.title("🚀 INVESTT Trading Bot - Live Trading")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Centrer la fenêtre
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        self.root.geometry(f"1400x900+{x}+{y}")
    
    def setup_variables(self):
        """Initialisation des variables"""
        self.is_live_trading = tk.BooleanVar(value=config.LIVE_TRADING)
        
        # Métriques
        self.portfolio_value = tk.StringVar(value=f"${config.INITIAL_CAPITAL:,.2f}")
        self.daily_pnl = tk.StringVar(value="$0.00")
        self.total_trades = tk.StringVar(value="0")
        self.win_rate = tk.StringVar(value="0%")
        self.status_text = tk.StringVar(value="🔴 Arrêté")
        
        # Configuration
        self.capital = tk.StringVar(value=str(config.INITIAL_CAPITAL))
        self.max_loss = tk.StringVar(value=str(config.MAX_DAILY_LOSS))
        self.position_size = tk.StringVar(value=str(config.MAX_POSITION_SIZE * 100))
    
    def create_widgets(self):
        """Création de l'interface utilisateur moderne"""
        
        # === HEADER ===
        header_frame = ctk.CTkFrame(self.root, height=80, corner_radius=0)
        header_frame.pack(fill="x", padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        # Titre principal
        title_label = ctk.CTkLabel(
            header_frame, 
            text="🚀 INVESTT TRADING BOT", 
            font=ctk.CTkFont(size=28, weight="bold")
        )
        title_label.pack(side="left", padx=20, pady=20)
        
        # Status en temps réel
        self.status_label = ctk.CTkLabel(
            header_frame,
            textvariable=self.status_text,
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.status_label.pack(side="right", padx=20, pady=20)
        
        # === MAIN CONTAINER ===
        main_container = ctk.CTkFrame(self.root, corner_radius=0)
        main_container.pack(fill="both", expand=True, padx=10, pady=10)
        
        # === LEFT PANEL - CONTRÔLES ===
        left_panel = ctk.CTkFrame(main_container, width=350)
        left_panel.pack(side="left", fill="y", padx=(0, 10))
        left_panel.pack_propagate(False)
        
        self.create_control_panel(left_panel)
        
        # === RIGHT PANEL - MONITORING ===
        right_panel = ctk.CTkFrame(main_container)
        right_panel.pack(side="right", fill="both", expand=True)
        
        self.create_monitoring_panel(right_panel)
    
    def create_control_panel(self, parent):
        """Panneau de contrôle moderne"""
        
        # Titre du panneau
        ctk.CTkLabel(
            parent, 
            text="⚙️ CONTRÔLES", 
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=(20, 10))
        
        # === MODE DE TRADING ===
        mode_frame = ctk.CTkFrame(parent)
        mode_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(
            mode_frame, 
            text="💰 Mode de Trading", 
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        # Switch Live/Paper Trading
        mode_switch = ctk.CTkSwitch(
            mode_frame,
            text="LIVE TRADING",
            variable=self.is_live_trading,
            command=self.toggle_trading_mode,
            font=ctk.CTkFont(size=14, weight="bold")
        )
        mode_switch.pack(pady=10)
        
        # Warning pour live trading
        self.warning_label = ctk.CTkLabel(
            mode_frame,
            text="⚠️ ARGENT RÉEL EN JEU !",
            text_color="red",
            font=ctk.CTkFont(size=12, weight="bold")
        )
        
        if self.is_live_trading.get():
            self.warning_label.pack(pady=5)
        
        # === BOUTONS PRINCIPAUX ===
        buttons_frame = ctk.CTkFrame(parent)
        buttons_frame.pack(fill="x", padx=20, pady=20)
        
        # Bouton START/STOP
        self.start_button = ctk.CTkButton(
            buttons_frame,
            text="🚀 DÉMARRER",
            command=self.toggle_bot,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=50,
            fg_color="green",
            hover_color="darkgreen"
        )
        self.start_button.pack(fill="x", pady=5)
        
        # Bouton PAUSE
        self.pause_button = ctk.CTkButton(
            buttons_frame,
            text="⏸️ PAUSE",
            command=self.pause_bot,
            font=ctk.CTkFont(size=14),
            height=40,
            state="disabled"
        )
        self.pause_button.pack(fill="x", pady=5)
        
        # Bouton ARRÊT D'URGENCE
        emergency_button = ctk.CTkButton(
            buttons_frame,
            text="🛑 ARRÊT D'URGENCE",
            command=self.emergency_stop,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40,
            fg_color="red",
            hover_color="darkred"
        )
        emergency_button.pack(fill="x", pady=5)
        
        # === CONFIGURATION ===
        config_frame = ctk.CTkFrame(parent)
        config_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(
            config_frame, 
            text="⚙️ Configuration", 
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        # Capital initial
        ctk.CTkLabel(config_frame, text="Capital Initial ($)").pack(anchor="w", padx=10)
        capital_entry = ctk.CTkEntry(config_frame, textvariable=self.capital)
        capital_entry.pack(fill="x", padx=10, pady=2)
        
        # Perte max quotidienne
        ctk.CTkLabel(config_frame, text="Perte Max Quotidienne ($)").pack(anchor="w", padx=10)
        loss_entry = ctk.CTkEntry(config_frame, textvariable=self.max_loss)
        loss_entry.pack(fill="x", padx=10, pady=2)
        
        # Taille de position
        ctk.CTkLabel(config_frame, text="Taille Position Max (%)").pack(anchor="w", padx=10)
        position_entry = ctk.CTkEntry(config_frame, textvariable=self.position_size)
        position_entry.pack(fill="x", padx=10, pady=(2, 10))
        
        # Bouton sauvegarder config
        save_config_btn = ctk.CTkButton(
            config_frame,
            text="💾 Sauvegarder Config",
            command=self.save_config,
            height=35
        )
        save_config_btn.pack(fill="x", padx=10, pady=(0, 10))
    
    def create_monitoring_panel(self, parent):
        """Panneau de monitoring moderne"""
        
        # Titre
        ctk.CTkLabel(
            parent, 
            text="📊 MONITORING EN TEMPS RÉEL", 
            font=ctk.CTkFont(size=20, weight="bold")
        ).pack(pady=(20, 10))
        
        # === MÉTRIQUES PRINCIPALES ===
        metrics_frame = ctk.CTkFrame(parent)
        metrics_frame.pack(fill="x", padx=20, pady=10)
        
        # Grille 2x2 pour les métriques
        metrics_grid = ctk.CTkFrame(metrics_frame)
        metrics_grid.pack(fill="x", padx=10, pady=10)
        
        # Portfolio Value
        portfolio_frame = ctk.CTkFrame(metrics_grid)
        portfolio_frame.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(portfolio_frame, text="💼 Portfolio", font=ctk.CTkFont(size=12)).pack()
        self.portfolio_label = ctk.CTkLabel(
            portfolio_frame, 
            textvariable=self.portfolio_value, 
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="lightblue"
        )
        self.portfolio_label.pack()
        
        # Daily P&L
        pnl_frame = ctk.CTkFrame(metrics_grid)
        pnl_frame.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(pnl_frame, text="📈 P&L Quotidien", font=ctk.CTkFont(size=12)).pack()
        self.pnl_label = ctk.CTkLabel(
            pnl_frame, 
            textvariable=self.daily_pnl, 
            font=ctk.CTkFont(size=18, weight="bold")
        )
        self.pnl_label.pack()
        
        # Total Trades
        trades_frame = ctk.CTkFrame(metrics_grid)
        trades_frame.grid(row=1, column=0, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(trades_frame, text="🔄 Trades Total", font=ctk.CTkFont(size=12)).pack()
        ctk.CTkLabel(
            trades_frame, 
            textvariable=self.total_trades, 
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="orange"
        ).pack()
        
        # Win Rate
        winrate_frame = ctk.CTkFrame(metrics_grid)
        winrate_frame.grid(row=1, column=1, padx=5, pady=5, sticky="ew")
        ctk.CTkLabel(winrate_frame, text="🎯 Win Rate", font=ctk.CTkFont(size=12)).pack()
        ctk.CTkLabel(
            winrate_frame, 
            textvariable=self.win_rate, 
            font=ctk.CTkFont(size=18, weight="bold"),
            text_color="lightgreen"
        ).pack()
        
        # Configuration de la grille
        metrics_grid.grid_columnconfigure(0, weight=1)
        metrics_grid.grid_columnconfigure(1, weight=1)
        
        # === LOG EN TEMPS RÉEL ===
        log_frame = ctk.CTkFrame(parent)
        log_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        ctk.CTkLabel(
            log_frame, 
            text="📝 Logs en Temps Réel", 
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(10, 5))
        
        # Zone de texte pour les logs
        self.log_text = ctk.CTkTextbox(
            log_frame,
            font=ctk.CTkFont(family="Consolas", size=11),
            wrap="word"
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
        # Bouton clear logs
        clear_btn = ctk.CTkButton(
            log_frame,
            text="🗑️ Effacer Logs",
            command=self.clear_logs,
            height=30
        )
        clear_btn.pack(pady=(0, 10))
    
    def toggle_trading_mode(self):
        """Basculer entre live et paper trading"""
        if self.is_live_trading.get():
            # Confirmation pour live trading
            result = messagebox.askyesno(
                "⚠️ CONFIRMATION LIVE TRADING",
                "ATTENTION: Vous allez activer le LIVE TRADING!\n\n"
                "Cela signifie que de l'ARGENT RÉEL sera utilisé.\n"
                "Êtes-vous sûr de vouloir continuer?",
                icon="warning"
            )
            
            if result:
                config.LIVE_TRADING = True
                config.PAPER_TRADING = False
                self.warning_label.pack(pady=5)
                self.log_message("⚠️ LIVE TRADING ACTIVÉ - ARGENT RÉEL!")
            else:
                self.is_live_trading.set(False)
        else:
            config.LIVE_TRADING = False
            config.PAPER_TRADING = True
            self.warning_label.pack_forget()
            self.log_message("📝 Paper Trading activé - Mode sécurisé")
    
    def toggle_bot(self):
        """Démarrer/Arrêter le bot"""
        if not self.bot_running:
            self.start_bot()
        else:
            self.stop_bot()
    
    def start_bot(self):
        """Démarrer le bot de trading"""
        try:
            # Validation avant démarrage
            if self.is_live_trading.get():
                if not config.BINANCE_API_KEY or not config.BINANCE_SECRET_KEY:
                    messagebox.showerror(
                        "Erreur Configuration",
                        "Clés API Binance manquantes pour le live trading!\n\n"
                        "Éditez le fichier .env avec vos vraies clés API."
                    )
                    return
            
            self.bot_running = True
            self.start_button.configure(
                text="🛑 ARRÊTER",
                fg_color="red",
                hover_color="darkred"
            )
            self.pause_button.configure(state="normal")
            self.status_text.set("🟢 En Fonctionnement")
            
            mode = "LIVE" if self.is_live_trading.get() else "PAPER"
            self.log_message(f"🚀 Bot démarré en mode {mode}")
            
            # Démarrer la simulation de trading
            self.start_trading_simulation()
            
        except Exception as e:
            self.log_message(f"❌ Erreur démarrage: {e}")
            messagebox.showerror("Erreur", f"Impossible de démarrer le bot:\n{e}")
    
    def stop_bot(self):
        """Arrêter le bot"""
        self.bot_running = False
        self.bot_paused = False
        
        self.start_button.configure(
            text="🚀 DÉMARRER",
            fg_color="green",
            hover_color="darkgreen"
        )
        self.pause_button.configure(state="disabled", text="⏸️ PAUSE")
        self.status_text.set("🔴 Arrêté")
        
        self.log_message("🛑 Bot arrêté")
    
    def pause_bot(self):
        """Mettre en pause/reprendre"""
        if self.bot_paused:
            self.bot_paused = False
            self.pause_button.configure(text="⏸️ PAUSE")
            self.status_text.set("🟢 En Fonctionnement")
            self.log_message("▶️ Bot repris")
        else:
            self.bot_paused = True
            self.pause_button.configure(text="▶️ REPRENDRE")
            self.status_text.set("⏸️ En Pause")
            self.log_message("⏸️ Bot en pause")
    
    def emergency_stop(self):
        """Arrêt d'urgence"""
        result = messagebox.askyesno(
            "🛑 ARRÊT D'URGENCE",
            "Voulez-vous vraiment effectuer un arrêt d'urgence?\n\n"
            "Cela fermera toutes les positions et arrêtera le bot immédiatement.",
            icon="warning"
        )
        
        if result:
            self.stop_bot()
            self.log_message("🛑 ARRÊT D'URGENCE EFFECTUÉ!")
            messagebox.showinfo("Arrêt d'Urgence", "Bot arrêté avec succès!")
    
    def save_config(self):
        """Sauvegarder la configuration"""
        try:
            config.INITIAL_CAPITAL = float(self.capital.get())
            config.MAX_DAILY_LOSS = float(self.max_loss.get())
            config.MAX_POSITION_SIZE = float(self.position_size.get()) / 100
            
            self.log_message("💾 Configuration sauvegardée")
            messagebox.showinfo("Configuration", "Configuration sauvegardée avec succès!")
            
        except ValueError:
            messagebox.showerror("Erreur", "Valeurs de configuration invalides!")
        except Exception as e:
            self.log_message(f"❌ Erreur sauvegarde: {e}")
    
    def start_trading_simulation(self):
        """Démarrer une simulation de trading"""
        def simulate():
            import random
            while self.bot_running:
                if not self.bot_paused:
                    # Simuler un trade occasionnel
                    if random.random() < 0.1:  # 10% de chance par cycle
                        self.trades_count += 1
                        
                        # Simuler P&L
                        trade_pnl = random.uniform(-20, 50)  # Entre -20€ et +50€
                        self.daily_pnl_num += trade_pnl
                        self.portfolio_value_num += trade_pnl
                        
                        # Mettre à jour l'affichage
                        self.root.after(0, self.update_display_after_trade, trade_pnl)
                
                time.sleep(5)  # Attendre 5 secondes
        
        # Lancer la simulation dans un thread
        sim_thread = threading.Thread(target=simulate, daemon=True)
        sim_thread.start()
    
    def update_display_after_trade(self, trade_pnl):
        """Mettre à jour l'affichage après un trade"""
        self.total_trades.set(str(self.trades_count))
        self.portfolio_value.set(f"${self.portfolio_value_num:,.2f}")
        
        # Couleur du P&L
        pnl_color = "lightgreen" if self.daily_pnl_num >= 0 else "red"
        self.daily_pnl.set(f"${self.daily_pnl_num:,.2f}")
        self.pnl_label.configure(text_color=pnl_color)
        
        # Win rate simulé
        win_rate = random.uniform(55, 75)
        self.win_rate.set(f"{win_rate:.1f}%")
        
        # Log du trade
        action = "Achat" if trade_pnl > 0 else "Vente"
        symbol = random.choice(config.TRADING_PAIRS)
        self.log_message(f"💰 {action} {symbol}: P&L {trade_pnl:+.2f}€")
    
    def log_message(self, message):
        """Ajouter un message au log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert("end", log_entry)
        self.log_text.see("end")
        
        # Limiter le nombre de lignes
        lines = self.log_text.get("1.0", "end").split("\n")
        if len(lines) > 100:
            self.log_text.delete("1.0", "10.0")
    
    def clear_logs(self):
        """Effacer les logs"""
        self.log_text.delete("1.0", "end")
        self.log_message("🗑️ Logs effacés")
    
    def start_update_loop(self):
        """Démarrer la boucle de mise à jour"""
        def update_loop():
            while True:
                try:
                    if self.bot_running and not self.bot_paused:
                        # Simulation de fluctuation du portfolio
                        if hasattr(self, 'portfolio_value_num'):
                            fluctuation = random.uniform(-0.5, 0.5)
                            self.portfolio_value_num += fluctuation
                            self.root.after(0, lambda: self.portfolio_value.set(f"${self.portfolio_value_num:,.2f}"))
                    
                    time.sleep(10)  # Mise à jour toutes les 10 secondes
                except:
                    break
        
        update_thread = threading.Thread(target=update_loop, daemon=True)
        update_thread.start()
    
    def on_closing(self):
        """Gestionnaire de fermeture"""
        if self.bot_running:
            result = messagebox.askyesno(
                "Fermeture",
                "Le bot est en cours d'exécution.\n"
                "Voulez-vous l'arrêter et fermer l'application?"
            )
            if result:
                self.stop_bot()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """Lancer l'application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("🚀 INVESTT Trading Bot démarré")
        self.log_message("💡 Configurez vos paramètres et cliquez sur DÉMARRER")
        self.log_message("⚠️ Version démo - Simulation de trading")
        self.root.mainloop()


def main():
    """Point d'entrée principal"""
    try:
        app = ModernTradingApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur Critique", f"Erreur lors du démarrage:\n{e}")


if __name__ == "__main__":
    main()
