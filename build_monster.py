"""
🤖👹 INVESTT MONSTER BOT - Build Script
Next Gen Trading Beast - DEX + Design de Malade + Scalping Ultra-Rapide
"""
import subprocess
import sys
import os
from pathlib import Path
import shutil

def print_monster_header():
    print("🤖👹 BUILD INVESTT MONSTER BOT")
    print("=" * 60)
    print("Next Gen Trading Beast")
    print("DEX + Design de Malade + Scalping Ultra-Rapide")
    print("=" * 60 + "\n")

def install_monster_dependencies():
    print("📦 Installation des dépendances MONSTER...")
    
    # Dépendances MONSTER
    monster_deps = [
        "customtkinter>=5.2.0",
        "pyinstaller>=5.13.0"
    ]
    
    success_count = 0
    
    for i, dep in enumerate(monster_deps, 1):
        try:
            print(f"   [{i}/{len(monster_deps)}] {dep.split('>=')[0]}...", end=" ")
            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", dep],
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅")
                success_count += 1
            else:
                print("❌")
                print(f"      Erreur: {result.stderr[:100]}...")
                
        except Exception as e:
            print(f"❌ {e}")
    
    print(f"\n📊 Installation terminée: {success_count}/{len(monster_deps)} paquets")
    return success_count >= len(monster_deps)

def create_monster_spec():
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['investt_monster.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'customtkinter',
        'tkinter',
        'threading',
        'time',
        'random',
        'datetime'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'pandas',
        'numpy',
        'scipy',
        'matplotlib',
        'seaborn',
        'plotly',
        'jupyter',
        'notebook',
        'IPython',
        'sklearn',
        'tensorflow',
        'torch',
        'keras',
        'PIL',
        'pillow',
        'math',
        'json'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='INVESTT_Monster_Trading_Bot',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open("monster.spec", "w") as f:
        f.write(spec_content)
    
    print("✅ Fichier .spec MONSTER créé")

def build_monster_exe():
    print("\n🔨 Construction de l'EXE MONSTER...")
    print("⏳ Cela devrait prendre 3-6 minutes...")
    
    try:
        result = subprocess.run(
            [sys.executable, "-m", "PyInstaller", "--clean", "monster.spec"],
            capture_output=True,
            text=True,
            timeout=600
        )
        
        if result.returncode == 0:
            print("✅ EXE MONSTER créé avec succès!")
            
            exe_path = Path("dist/INVESTT_Monster_Trading_Bot.exe")
            if exe_path.exists():
                size_mb = exe_path.stat().st_size / (1024 * 1024)
                print(f"📁 Fichier: {exe_path}")
                print(f"📊 Taille: {size_mb:.1f} MB")
                return True
            else:
                print("❌ Fichier EXE non trouvé")
                return False
        else:
            print(f"❌ Erreur PyInstaller:")
            stderr_lines = result.stderr.split('\n')
            for line in stderr_lines[-10:]:
                if any(keyword in line for keyword in ['ERROR', 'CRITICAL', 'ModuleNotFoundError', 'ImportError']):
                    print(f"   {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Timeout")
        return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def create_monster_package():
    print("\n📦 Création du package MONSTER...")
    
    try:
        monster_folder = Path("INVESTT_Monster_Final")
        if monster_folder.exists():
            shutil.rmtree(monster_folder)
        
        monster_folder.mkdir()
        
        # Copier l'EXE
        exe_source = Path("dist/INVESTT_Monster_Trading_Bot.exe")
        if exe_source.exists():
            shutil.copy2(exe_source, monster_folder / "INVESTT_Monster_Trading_Bot.exe")
            print("✅ EXE MONSTER copié")
        
        # Guide MONSTER
        monster_guide = '''🤖👹 INVESTT MONSTER BOT - GUIDE BEAST MODE

═══════════════════════════════════════════════════════════════

🔥 VERSION MONSTER - NEXT GEN TRADING BEAST:
   • DEX Trading intégré (Uniswap, PancakeSwap, etc.)
   • Design cyberpunk de MALADE avec animations
   • Scalping ultra-rapide (trades en millisecondes)
   • Pump Hunter avec détection automatique
   • Shitcoin Hunter pour volatilité maximale
   • Beast Mode pour trading ultra-agressif
   • Interface 3 panneaux avec bordures colorées
   • IA Monster avec apprentissage adaptatif

🎯 DÉMARRAGE MONSTER:
   1. Double-cliquez sur "INVESTT_Monster_Trading_Bot.exe"
   2. Interface cyberpunk s'ouvre avec effets visuels
   3. Configurez vos modes Monster (Pump Hunter, Shitcoin Hunter, etc.)
   4. Activez le Beast Mode si vous osez
   5. Cliquez sur "UNLEASH THE MONSTER"
   6. Regardez le chaos se déchaîner!

⚙️ MODES MONSTER:
   🔗 DEX Mode: Trading décentralisé sans KYC
   👹 Beast Mode: Trading ultra-agressif (DANGER!)
   🚀 Pump Hunter: Détection automatique des pumps
   💩 Shitcoin Hunter: Chasse aux coins volatiles
   ⚡ Lightning Scalp: Trades ultra-rapides

🤖 IA MONSTER:
   • Confiance Monster: 85-95% (s'améliore)
   • Détection Pump: 80-95% (ultra-rapide)
   • Score Shitcoin: 0-100% (volatilité)
   • Plus Gros Pump: Mémorise les records
   • Apprentissage Beast: Adaptation agressive

🎨 DESIGN DE MALADE:
   • Interface cyberpunk avec bordures colorées
   • Animations en temps réel
   • Couleurs Monster (vert, rouge, jaune, magenta)
   • Effets visuels selon les modes
   • Status Beast Mode avec animations

💰 TRADING MONSTER:
   • Pairs Monster: BTC, ETH, SOL + Shitcoins (PEPE, SHIB, DOGE, etc.)
   • Scalping 1 seconde: Trades ultra-rapides
   • Profit minimum: 0.2% (ajustable)
   • Hold time max: 30 secondes
   • Slippage: 0.5% (DEX optimisé)

🔧 CONFIGURATION DEX:
   1. Saisissez votre adresse wallet
   2. Configurez le slippage (0.5% recommandé)
   3. Définissez le profit minimum
   4. Ajustez le temps de hold maximum
   5. Activez les modes souhaités

🛡️ SÉCURITÉ MONSTER:
   ⚠️ Beast Mode = Trading ULTRA-AGRESSIF
   ⚠️ DEX Mode = Votre wallet, votre responsabilité
   ⚠️ Shitcoins = Volatilité EXTRÊME
   ⚠️ Testez d'abord en mode simulation
   ⚠️ Arrêt d'urgence toujours disponible

🚀 FONCTIONNALITÉS AVANCÉES:
   • Détection de pumps en temps réel
   • Scanner de shitcoins automatique
   • Optimisation IA continue
   • Volume trading tracking
   • Métriques Beast Mode
   • Logs colorés en temps réel

💡 UTILISATION OPTIMALE:

   Phase 1: Découverte (30 min)
   - Interface cyberpunk impressionnante
   - Testez les différents modes
   - Observez les animations
   - Familiarisez-vous avec les couleurs

   Phase 2: Configuration (15 min)
   - Configurez votre wallet DEX
   - Ajustez les paramètres de trading
   - Choisissez vos modes Monster
   - Testez en simulation d'abord

   Phase 3: Beast Mode (Après maîtrise)
   - Activez le Beast Mode avec précaution
   - Surveillez les métriques en temps réel
   - Laissez le Monster chasser les pumps
   - Profitez du chaos contrôlé!

🎯 AVANTAGES MONSTER:
   ✅ Design le plus fou du marché
   ✅ DEX trading sans limites
   ✅ Scalping ultra-rapide
   ✅ Détection de pumps automatique
   ✅ Shitcoin hunting intelligent
   ✅ Beast Mode pour les audacieux
   ✅ Interface cyberpunk unique

⚡ PERFORMANCES MONSTER:
   • Trades: Jusqu'à 30 par minute en Beast Mode
   • Vitesse: Analyse en millisecondes
   • Précision: 75-90% selon les conditions
   • Volume: Tracking complet
   • Gains: Amplifiés par les multiplicateurs Monster

🔥 MULTIPLICATEURS MONSTER:
   • Pump détecté: x2-5 gains
   • Shitcoin: x1.5-3 gains
   • Beast Mode: x1.2-2 gains
   • Volume Spike: x1.1 gains
   • Combo: Multiplicateurs cumulés!

═══════════════════════════════════════════════════════════════
🤖👹 MONSTER = DESIGN DE MALADE + DEX + SCALPING BEAST ! 🔥💰
═══════════════════════════════════════════════════════════════
'''
        
        with open(monster_folder / "GUIDE_MONSTER.txt", "w", encoding='utf-8') as f:
            f.write(monster_guide)
        print("✅ Guide MONSTER créé")
        
        # Lanceur MONSTER
        monster_launcher = '''@echo off
title INVESTT MONSTER BOT - Beast Mode
color 0A
echo.
echo ████████████████████████████████████████████████████████████
echo █                                                          █
echo █    🤖👹 INVESTT MONSTER - BEAST MODE TRADING          █
echo █                                                          █
echo ████████████████████████████████████████████████████████████
echo.
echo 🔥 Unleashing the Monster...
echo 🚀 DEX Trading + Design de Malade
echo ⚡ Scalping Ultra-Rapide activé
echo 💩 Shitcoin Hunter prêt
echo 👹 Beast Mode disponible
echo.
start "" "INVESTT_Monster_Trading_Bot.exe"
echo ✅ Monster unleashed successfully!
echo.
echo 💡 Interface cyberpunk avec IA Monster
echo 🔥 Prêt pour le chaos contrôlé!
echo.
timeout /t 3 >nul
'''
        
        with open(monster_folder / "Unleash_Monster.bat", "w") as f:
            f.write(monster_launcher)
        print("✅ Lanceur MONSTER créé")
        
        print(f"\n✅ Package MONSTER complet créé: {monster_folder}")
        return True
        
    except Exception as e:
        print(f"❌ Erreur création package MONSTER: {e}")
        return False

def main():
    print_monster_header()
    
    if not Path("investt_monster.py").exists():
        print("❌ Fichier investt_monster.py non trouvé!")
        input("Appuyez sur Entrée pour fermer...")
        return
    
    steps = [
        ("Installation dépendances MONSTER", install_monster_dependencies),
        ("Création fichier .spec MONSTER", create_monster_spec),
        ("Construction EXE MONSTER", build_monster_exe),
        ("Création package MONSTER", create_monster_package)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"🔄 {step_name}...")
        try:
            if step_func in [install_monster_dependencies, build_monster_exe, create_monster_package]:
                success = step_func()
                if success:
                    success_count += 1
                elif step_func == build_monster_exe:
                    break
            else:
                step_func()
                success_count += 1
        except Exception as e:
            print(f"❌ Erreur dans {step_name}: {e}")
    
    print("\n" + "=" * 70)
    print("🤖👹 BUILD MONSTER TERMINÉ!")
    print("=" * 70)
    
    final_exe = Path("INVESTT_Monster_Final/INVESTT_Monster_Trading_Bot.exe")
    if final_exe.exists():
        print("✅ MONSTER BOT CRÉÉ AVEC SUCCÈS!")
        print(f"\n📁 Dossier: INVESTT_Monster_Final/")
        print(f"🤖 Fichier: INVESTT_Monster_Trading_Bot.exe")
        print(f"📊 Taille: {final_exe.stat().st_size / (1024*1024):.1f} MB")
        
        print("\n🔥 FONCTIONNALITÉS MONSTER:")
        print("   ✅ Interface cyberpunk de MALADE")
        print("   ✅ DEX Trading sans KYC")
        print("   ✅ Scalping ultra-rapide")
        print("   ✅ Pump Hunter automatique")
        print("   ✅ Shitcoin Hunter intelligent")
        print("   ✅ Beast Mode ultra-agressif")
        
        print("\n🚀 UTILISATION:")
        print("   1. Allez dans 'INVESTT_Monster_Final'")
        print("   2. Double-cliquez sur 'Unleash_Monster.bat'")
        print("   3. Interface cyberpunk s'ouvre")
        print("   4. Configurez vos modes Monster")
        print("   5. Cliquez sur 'UNLEASH THE MONSTER'")
        print("   6. Regardez le chaos se déchaîner!")
        
        print("\n💡 AVANTAGES MONSTER:")
        print("   🤖 Design cyberpunk unique")
        print("   🔗 DEX trading décentralisé")
        print("   ⚡ Scalping millisecondes")
        print("   🚀 Détection pumps auto")
        print("   💩 Chasse shitcoins intelligente")
        print("   👹 Beast Mode pour les audacieux")
        
        print("\n🎯 RÉVOLUTION:")
        print("   ✅ Plus de KYC - Pure liberté")
        print("   ✅ Design le plus fou du marché")
        print("   ✅ Scalping ultra-rapide")
        print("   ✅ IA Monster adaptative")
        print("   ✅ Beast Mode chaos contrôlé")
        
    else:
        print("⚠️ EXE MONSTER non créé")
        print(f"✅ Étapes réussies: {success_count}/{len(steps)}")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ Build MONSTER interrompu")
    except Exception as e:
        print(f"\n\n❌ Erreur critique: {e}")
    finally:
        input("\nAppuyez sur Entrée pour fermer...")
