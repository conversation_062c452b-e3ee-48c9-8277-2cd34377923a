"""
INVESTT Trading Bot Dashboard
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import time

from config import config
from src.database import session_scope, Trade, Position, PerformanceMetrics
from src.trading import trading_engine
from src.data import market_data_manager


# Page configuration
st.set_page_config(
    page_title="INVESTT Trading Bot",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .positive { color: #00ff00; }
    .negative { color: #ff0000; }
    .neutral { color: #888888; }
</style>
""", unsafe_allow_html=True)


def load_data():
    """Load data from database"""
    try:
        with session_scope() as session:
            # Get recent trades
            trades = session.query(Trade).order_by(Trade.timestamp.desc()).limit(100).all()
            trades_df = pd.DataFrame([
                {
                    'timestamp': trade.timestamp,
                    'symbol': trade.symbol,
                    'side': trade.side,
                    'amount': trade.amount,
                    'price': trade.price,
                    'value': trade.value,
                    'pnl': trade.pnl,
                    'strategy': trade.strategy_name
                }
                for trade in trades
            ])
            
            # Get current positions
            positions = session.query(Position).all()
            positions_df = pd.DataFrame([
                {
                    'symbol': pos.symbol,
                    'side': pos.side,
                    'amount': pos.amount,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'unrealized_pnl_pct': pos.unrealized_pnl_percentage,
                    'opened_at': pos.opened_at
                }
                for pos in positions
            ])
            
            # Get performance metrics
            performance = session.query(PerformanceMetrics).order_by(
                PerformanceMetrics.date.desc()
            ).limit(30).all()
            
            performance_df = pd.DataFrame([
                {
                    'date': perf.date,
                    'total_value': perf.total_value,
                    'daily_pnl': perf.daily_pnl,
                    'total_pnl': perf.total_pnl,
                    'trades_count': perf.trades_count,
                    'win_rate': perf.winning_trades / max(perf.trades_count, 1) * 100
                }
                for perf in performance
            ])
            
            return trades_df, positions_df, performance_df
            
    except Exception as e:
        st.error(f"Error loading data: {e}")
        return pd.DataFrame(), pd.DataFrame(), pd.DataFrame()


def main():
    """Main dashboard function"""
    
    # Header
    st.title("🚀 INVESTT Trading Bot Dashboard")
    
    # Sidebar
    st.sidebar.title("Controls")
    
    # Auto-refresh
    auto_refresh = st.sidebar.checkbox("Auto Refresh (30s)", value=True)
    if auto_refresh:
        time.sleep(30)
        st.rerun()
    
    # Manual refresh
    if st.sidebar.button("Refresh Now"):
        st.rerun()
    
    # Trading controls
    st.sidebar.subheader("Trading Controls")
    
    # Get trading engine status
    status = trading_engine.get_status()
    
    # Status indicators
    if status['is_running']:
        st.sidebar.success("🟢 Bot is Running")
        if st.sidebar.button("Stop Bot"):
            trading_engine.stop()
            st.rerun()
    else:
        st.sidebar.error("🔴 Bot is Stopped")
        if st.sidebar.button("Start Bot"):
            trading_engine.start()
            st.rerun()
    
    if status['is_paused']:
        st.sidebar.warning("⏸️ Bot is Paused")
        if st.sidebar.button("Resume"):
            trading_engine.resume()
            st.rerun()
    else:
        if status['is_running'] and st.sidebar.button("Pause"):
            trading_engine.pause()
            st.rerun()
    
    # Load data
    trades_df, positions_df, performance_df = load_data()
    
    # Main metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        portfolio_value = status.get('portfolio_value', config.INITIAL_CAPITAL)
        st.metric(
            "Portfolio Value",
            f"${portfolio_value:,.2f}",
            f"${portfolio_value - config.INITIAL_CAPITAL:,.2f}"
        )
    
    with col2:
        daily_pnl = status.get('current_pnl', 0)
        pnl_color = "positive" if daily_pnl > 0 else "negative" if daily_pnl < 0 else "neutral"
        st.metric(
            "Daily P&L",
            f"${daily_pnl:,.2f}",
            f"{daily_pnl/config.INITIAL_CAPITAL*100:.2f}%"
        )
    
    with col3:
        total_trades = status.get('total_trades', 0)
        successful_trades = status.get('successful_trades', 0)
        win_rate = (successful_trades / max(total_trades, 1)) * 100
        st.metric(
            "Total Trades",
            total_trades,
            f"{win_rate:.1f}% win rate"
        )
    
    with col4:
        active_positions = len(positions_df)
        st.metric(
            "Active Positions",
            active_positions,
            f"Max: {config.MAX_CONCURRENT_TRADES}"
        )
    
    # Performance chart
    st.subheader("📈 Performance Overview")
    
    if not performance_df.empty:
        fig = go.Figure()
        
        # Portfolio value line
        fig.add_trace(go.Scatter(
            x=performance_df['date'],
            y=performance_df['total_value'],
            mode='lines+markers',
            name='Portfolio Value',
            line=dict(color='blue', width=2)
        ))
        
        # Add initial capital line
        fig.add_hline(
            y=config.INITIAL_CAPITAL,
            line_dash="dash",
            line_color="gray",
            annotation_text="Initial Capital"
        )
        
        fig.update_layout(
            title="Portfolio Value Over Time",
            xaxis_title="Date",
            yaxis_title="Value ($)",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    else:
        st.info("No performance data available yet")
    
    # Current positions
    st.subheader("💼 Current Positions")
    
    if not positions_df.empty:
        # Format the dataframe for display
        display_positions = positions_df.copy()
        display_positions['Entry Price'] = display_positions['entry_price'].apply(lambda x: f"${x:.4f}")
        display_positions['Current Price'] = display_positions['current_price'].apply(lambda x: f"${x:.4f}")
        display_positions['Unrealized P&L'] = display_positions['unrealized_pnl'].apply(lambda x: f"${x:.2f}")
        display_positions['P&L %'] = display_positions['unrealized_pnl_pct'].apply(lambda x: f"{x:.2f}%")
        display_positions['Amount'] = display_positions['amount'].apply(lambda x: f"{x:.6f}")
        
        # Select columns for display
        display_columns = ['symbol', 'side', 'Amount', 'Entry Price', 'Current Price', 'Unrealized P&L', 'P&L %']
        st.dataframe(
            display_positions[display_columns],
            use_container_width=True,
            hide_index=True
        )
    else:
        st.info("No active positions")
    
    # Recent trades
    st.subheader("📊 Recent Trades")
    
    if not trades_df.empty:
        # Show last 10 trades
        recent_trades = trades_df.head(10).copy()
        recent_trades['Timestamp'] = recent_trades['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S')
        recent_trades['Price'] = recent_trades['price'].apply(lambda x: f"${x:.4f}")
        recent_trades['Value'] = recent_trades['value'].apply(lambda x: f"${x:.2f}")
        recent_trades['P&L'] = recent_trades['pnl'].apply(lambda x: f"${x:.2f}" if pd.notna(x) else "N/A")
        recent_trades['Amount'] = recent_trades['amount'].apply(lambda x: f"{x:.6f}")
        
        display_columns = ['Timestamp', 'symbol', 'side', 'Amount', 'Price', 'Value', 'P&L', 'strategy']
        st.dataframe(
            recent_trades[display_columns],
            use_container_width=True,
            hide_index=True
        )
    else:
        st.info("No trades executed yet")
    
    # Strategy performance
    st.subheader("🎯 Strategy Performance")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Strategy Status")
        for strategy in trading_engine.strategies:
            status_icon = "🟢" if strategy.enabled else "🔴"
            st.write(f"{status_icon} **{strategy.name}**")
            st.write(f"   - Symbols: {len(strategy.symbols)}")
            st.write(f"   - Timeframe: {strategy.timeframe}")
            
            # Strategy metrics
            metrics = strategy.get_performance_metrics()
            st.write(f"   - Signals: {metrics['total_signals']}")
            st.write(f"   - Win Rate: {metrics['win_rate']:.1%}")
            st.write(f"   - P&L: ${metrics['total_pnl']:.2f}")
    
    with col2:
        st.subheader("Risk Status")
        risk_status = status.get('risk_status', {})
        
        # Daily P&L vs limit
        daily_pnl = risk_status.get('daily_pnl', 0)
        daily_limit = risk_status.get('daily_loss_limit', config.MAX_DAILY_LOSS)
        pnl_pct = abs(daily_pnl) / daily_limit * 100 if daily_limit > 0 else 0
        
        st.metric(
            "Daily P&L vs Limit",
            f"${daily_pnl:.2f}",
            f"{pnl_pct:.1f}% of limit"
        )
        
        # Position count vs limit
        active_pos = risk_status.get('active_positions', 0)
        max_pos = risk_status.get('max_concurrent_trades', config.MAX_CONCURRENT_TRADES)
        
        st.metric(
            "Positions",
            f"{active_pos}/{max_pos}",
            f"{active_pos/max_pos*100:.1f}% capacity"
        )
        
        # Trading status
        trading_allowed = risk_status.get('trading_allowed', True)
        if trading_allowed:
            st.success("✅ Trading Allowed")
        else:
            st.error("❌ Trading Blocked")
    
    # Configuration
    with st.expander("⚙️ Configuration"):
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Trading Settings**")
            st.write(f"Mode: {'Live' if config.LIVE_TRADING else 'Paper'}")
            st.write(f"Exchange: Binance {'(Testnet)' if config.BINANCE_TESTNET else ''}")
            st.write(f"Initial Capital: ${config.INITIAL_CAPITAL:,.2f}")
            st.write(f"Max Daily Loss: ${config.MAX_DAILY_LOSS:,.2f}")
            st.write(f"Max Position Size: {config.MAX_POSITION_SIZE:.1%}")
        
        with col2:
            st.write("**Strategy Settings**")
            st.write(f"Primary Timeframe: {config.PRIMARY_TIMEFRAME}")
            st.write(f"Trading Pairs: {', '.join(config.TRADING_PAIRS)}")
            st.write(f"RSI Period: {config.RSI_PERIOD}")
            st.write(f"RSI Oversold: {config.RSI_OVERSOLD}")
            st.write(f"RSI Overbought: {config.RSI_OVERBOUGHT}")


if __name__ == "__main__":
    main()
