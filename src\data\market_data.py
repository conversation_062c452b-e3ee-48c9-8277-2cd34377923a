"""
Market data management and real-time data feeds
"""
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Callable, Any
import ccxt
import websocket
import json
import threading
from dataclasses import dataclass

from config import config
from src.utils.logger import log_error, log_trade, logger
from src.database import session_scope, MarketData


@dataclass
class OHLCV:
    """OHLCV data structure"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    symbol: str
    timeframe: str


@dataclass
class Ticker:
    """Real-time ticker data"""
    symbol: str
    bid: float
    ask: float
    last: float
    volume: float
    timestamp: datetime


class MarketDataManager:
    """Manages market data collection and distribution"""
    
    def __init__(self):
        self.exchange = None
        self.websocket_connections = {}
        self.subscribers = {}  # symbol -> list of callbacks
        self.data_cache = {}  # symbol -> latest data
        self.is_running = False
        self.initialize_exchange()
    
    def initialize_exchange(self):
        """Initialize exchange connection"""
        try:
            if config.BINANCE_TESTNET:
                self.exchange = ccxt.binance({
                    'apiKey': config.BINANCE_API_KEY,
                    'secret': config.BINANCE_SECRET_KEY,
                    'sandbox': True,  # Use testnet
                    'enableRateLimit': True,
                })
            else:
                self.exchange = ccxt.binance({
                    'apiKey': config.BINANCE_API_KEY,
                    'secret': config.BINANCE_SECRET_KEY,
                    'enableRateLimit': True,
                })
            
            # Test connection
            self.exchange.load_markets()
            logger.info(f"Connected to {self.exchange.name} exchange")
            
        except Exception as e:
            log_error("Failed to initialize exchange", e)
            raise
    
    def get_historical_data(self, symbol: str, timeframe: str, limit: int = 500) -> pd.DataFrame:
        """Get historical OHLCV data"""
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Store in database
            self._store_historical_data(symbol, timeframe, df)
            
            return df
            
        except Exception as e:
            log_error(f"Failed to get historical data for {symbol}", e)
            return pd.DataFrame()
    
    def _store_historical_data(self, symbol: str, timeframe: str, df: pd.DataFrame):
        """Store historical data in database"""
        try:
            with session_scope() as session:
                for timestamp, row in df.iterrows():
                    # Check if data already exists
                    existing = session.query(MarketData).filter_by(
                        symbol=symbol,
                        timeframe=timeframe,
                        timestamp=timestamp
                    ).first()
                    
                    if not existing:
                        market_data = MarketData(
                            symbol=symbol,
                            timeframe=timeframe,
                            timestamp=timestamp,
                            open=row['open'],
                            high=row['high'],
                            low=row['low'],
                            close=row['close'],
                            volume=row['volume']
                        )
                        session.add(market_data)
                
                session.commit()
                
        except Exception as e:
            log_error(f"Failed to store historical data for {symbol}", e)
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price for a symbol"""
        try:
            ticker = self.exchange.fetch_ticker(symbol)
            return ticker['last']
        except Exception as e:
            log_error(f"Failed to get current price for {symbol}", e)
            return None
    
    def get_order_book(self, symbol: str, limit: int = 10) -> Optional[Dict]:
        """Get order book for a symbol"""
        try:
            return self.exchange.fetch_order_book(symbol, limit)
        except Exception as e:
            log_error(f"Failed to get order book for {symbol}", e)
            return None
    
    def subscribe_to_ticker(self, symbol: str, callback: Callable[[Ticker], None]):
        """Subscribe to real-time ticker updates"""
        if symbol not in self.subscribers:
            self.subscribers[symbol] = []
        
        self.subscribers[symbol].append(callback)
        
        # Start WebSocket connection if not already running
        if symbol not in self.websocket_connections:
            self._start_websocket_ticker(symbol)
    
    def _start_websocket_ticker(self, symbol: str):
        """Start WebSocket connection for ticker data"""
        def on_message(ws, message):
            try:
                data = json.loads(message)
                
                if 'c' in data:  # Binance ticker format
                    ticker = Ticker(
                        symbol=symbol,
                        bid=float(data.get('b', 0)),
                        ask=float(data.get('a', 0)),
                        last=float(data.get('c', 0)),
                        volume=float(data.get('v', 0)),
                        timestamp=datetime.utcnow()
                    )
                    
                    # Update cache
                    self.data_cache[symbol] = ticker
                    
                    # Notify subscribers
                    for callback in self.subscribers.get(symbol, []):
                        try:
                            callback(ticker)
                        except Exception as e:
                            log_error(f"Error in ticker callback for {symbol}", e)
                            
            except Exception as e:
                log_error(f"Error processing WebSocket message for {symbol}", e)
        
        def on_error(ws, error):
            log_error(f"WebSocket error for {symbol}", error)
        
        def on_close(ws, close_status_code, close_msg):
            logger.warning(f"WebSocket connection closed for {symbol}")
            # Attempt to reconnect after 5 seconds
            threading.Timer(5.0, lambda: self._start_websocket_ticker(symbol)).start()
        
        def on_open(ws):
            logger.info(f"WebSocket connection opened for {symbol}")
        
        # Convert symbol format for Binance WebSocket
        ws_symbol = symbol.replace('/', '').lower()
        ws_url = f"wss://stream.binance.com:9443/ws/{ws_symbol}@ticker"
        
        ws = websocket.WebSocketApp(
            ws_url,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close,
            on_open=on_open
        )
        
        # Start WebSocket in a separate thread
        def run_websocket():
            ws.run_forever()
        
        ws_thread = threading.Thread(target=run_websocket, daemon=True)
        ws_thread.start()
        
        self.websocket_connections[symbol] = ws
    
    def get_cached_ticker(self, symbol: str) -> Optional[Ticker]:
        """Get cached ticker data"""
        return self.data_cache.get(symbol)
    
    def start(self):
        """Start the market data manager"""
        self.is_running = True
        logger.info("Market data manager started")
    
    def stop(self):
        """Stop the market data manager"""
        self.is_running = False
        
        # Close all WebSocket connections
        for symbol, ws in self.websocket_connections.items():
            try:
                ws.close()
            except Exception as e:
                log_error(f"Error closing WebSocket for {symbol}", e)
        
        self.websocket_connections.clear()
        logger.info("Market data manager stopped")


# Global market data manager instance
market_data_manager = MarketDataManager()


def get_market_data_manager() -> MarketDataManager:
    """Get the global market data manager"""
    return market_data_manager
