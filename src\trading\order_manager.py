"""
Order management system for executing trades
"""
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import ccxt

from config import config
from src.utils.logger import log_order, log_error, logger
from src.database import session_scope, Order, OrderStatus, OrderType, OrderSide


@dataclass
class OrderRequest:
    """Order request structure"""
    symbol: str
    side: str  # 'buy' or 'sell'
    order_type: str  # 'market', 'limit', 'stop_loss', 'take_profit'
    amount: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    strategy_name: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class OrderResult:
    """Order execution result"""
    success: bool
    order_id: Optional[str] = None
    exchange_order_id: Optional[str] = None
    filled_amount: float = 0.0
    average_price: Optional[float] = None
    fee: float = 0.0
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class OrderManager:
    """Manages order execution and tracking"""
    
    def __init__(self):
        self.exchange = None
        self.pending_orders: Dict[str, Order] = {}
        self.order_history: List[Order] = []
        self.initialize_exchange()
    
    def initialize_exchange(self):
        """Initialize exchange connection for order execution"""
        try:
            if config.BINANCE_TESTNET:
                self.exchange = ccxt.binance({
                    'apiKey': config.BINANCE_API_KEY,
                    'secret': config.BINANCE_SECRET_KEY,
                    'sandbox': True,
                    'enableRateLimit': True,
                })
            else:
                self.exchange = ccxt.binance({
                    'apiKey': config.BINANCE_API_KEY,
                    'secret': config.BINANCE_SECRET_KEY,
                    'enableRateLimit': True,
                })
            
            # Test connection
            if config.LIVE_TRADING:
                self.exchange.load_markets()
                balance = self.exchange.fetch_balance()
                logger.info(f"Connected to {self.exchange.name} for live trading")
            else:
                logger.info(f"Order manager initialized for paper trading")
                
        except Exception as e:
            log_error("Failed to initialize exchange for order management", e)
            if config.LIVE_TRADING:
                raise
    
    def place_order(self, order_request: OrderRequest) -> Optional[str]:
        """Place an order"""
        try:
            # Create order record
            order = Order(
                symbol=order_request.symbol,
                side=order_request.side,
                order_type=order_request.order_type,
                amount=order_request.amount,
                price=order_request.price,
                strategy_name=order_request.strategy_name,
                status=OrderStatus.PENDING
            )
            
            # Save to database
            with session_scope() as session:
                session.add(order)
                session.commit()
                order_id = str(order.id)
            
            # Execute order
            if config.LIVE_TRADING and self.exchange:
                result = self._execute_live_order(order_request, order_id)
            else:
                result = self._execute_paper_order(order_request, order_id)
            
            # Update order status
            self._update_order_status(order_id, result)
            
            if result.success:
                log_order(
                    order_request.order_type,
                    order_request.symbol,
                    f"Order placed successfully: {order_request.side} {order_request.amount} at {order_request.price}"
                )
                return order_id
            else:
                log_error(f"Order failed: {result.error_message}")
                return None
                
        except Exception as e:
            log_error(f"Error placing order for {order_request.symbol}", e)
            return None
    
    def _execute_live_order(self, order_request: OrderRequest, order_id: str) -> OrderResult:
        """Execute order on live exchange"""
        try:
            if order_request.order_type == 'market':
                order = self.exchange.create_market_order(
                    order_request.symbol,
                    order_request.side,
                    order_request.amount
                )
            elif order_request.order_type == 'limit':
                order = self.exchange.create_limit_order(
                    order_request.symbol,
                    order_request.side,
                    order_request.amount,
                    order_request.price
                )
            else:
                return OrderResult(
                    success=False,
                    error_message=f"Unsupported order type: {order_request.order_type}"
                )
            
            return OrderResult(
                success=True,
                order_id=order_id,
                exchange_order_id=order['id'],
                filled_amount=order.get('filled', 0),
                average_price=order.get('average'),
                fee=order.get('fee', {}).get('cost', 0),
                metadata=order
            )
            
        except Exception as e:
            return OrderResult(
                success=False,
                error_message=str(e)
            )
    
    def _execute_paper_order(self, order_request: OrderRequest, order_id: str) -> OrderResult:
        """Execute paper order (simulation)"""
        try:
            # Simulate order execution
            filled_amount = order_request.amount
            average_price = order_request.price
            
            # Simulate fee (0.1% for Binance)
            fee = filled_amount * average_price * 0.001
            
            return OrderResult(
                success=True,
                order_id=order_id,
                exchange_order_id=f"paper_{order_id}_{int(time.time())}",
                filled_amount=filled_amount,
                average_price=average_price,
                fee=fee,
                metadata={'paper_trade': True}
            )
            
        except Exception as e:
            return OrderResult(
                success=False,
                error_message=str(e)
            )
    
    def _update_order_status(self, order_id: str, result: OrderResult):
        """Update order status in database"""
        try:
            with session_scope() as session:
                order = session.query(Order).filter(Order.id == int(order_id)).first()
                if order:
                    if result.success:
                        order.status = OrderStatus.FILLED
                        order.exchange_order_id = result.exchange_order_id
                        order.filled_amount = result.filled_amount
                        order.average_price = result.average_price
                        order.fee = result.fee
                        order.filled_at = datetime.utcnow()
                    else:
                        order.status = OrderStatus.REJECTED
                    
                    order.updated_at = datetime.utcnow()
                    session.commit()
                    
        except Exception as e:
            log_error(f"Error updating order status for {order_id}", e)
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            with session_scope() as session:
                order = session.query(Order).filter(Order.id == int(order_id)).first()
                if not order:
                    return False
                
                # Cancel on exchange if live trading
                if config.LIVE_TRADING and order.exchange_order_id:
                    try:
                        self.exchange.cancel_order(order.exchange_order_id, order.symbol)
                    except Exception as e:
                        log_error(f"Error canceling order on exchange: {e}")
                        return False
                
                # Update status
                order.status = OrderStatus.CANCELLED
                order.updated_at = datetime.utcnow()
                session.commit()
                
                log_order("CANCEL", order.symbol, f"Order {order_id} cancelled")
                return True
                
        except Exception as e:
            log_error(f"Error canceling order {order_id}", e)
            return False
    
    def get_order_status(self, order_id: str) -> Optional[Dict]:
        """Get order status"""
        try:
            with session_scope() as session:
                order = session.query(Order).filter(Order.id == int(order_id)).first()
                if order:
                    return {
                        'id': order.id,
                        'symbol': order.symbol,
                        'side': order.side,
                        'order_type': order.order_type,
                        'amount': order.amount,
                        'price': order.price,
                        'status': order.status,
                        'filled_amount': order.filled_amount,
                        'average_price': order.average_price,
                        'fee': order.fee,
                        'created_at': order.created_at,
                        'updated_at': order.updated_at,
                        'filled_at': order.filled_at
                    }
                return None
                
        except Exception as e:
            log_error(f"Error getting order status for {order_id}", e)
            return None
    
    def get_open_orders(self, symbol: Optional[str] = None) -> List[Dict]:
        """Get open orders"""
        try:
            with session_scope() as session:
                query = session.query(Order).filter(Order.status == OrderStatus.PENDING)
                if symbol:
                    query = query.filter(Order.symbol == symbol)
                
                orders = query.all()
                return [
                    {
                        'id': order.id,
                        'symbol': order.symbol,
                        'side': order.side,
                        'order_type': order.order_type,
                        'amount': order.amount,
                        'price': order.price,
                        'created_at': order.created_at
                    }
                    for order in orders
                ]
                
        except Exception as e:
            log_error("Error getting open orders", e)
            return []
    
    def get_order_history(self, symbol: Optional[str] = None, limit: int = 100) -> List[Dict]:
        """Get order history"""
        try:
            with session_scope() as session:
                query = session.query(Order).order_by(Order.created_at.desc())
                if symbol:
                    query = query.filter(Order.symbol == symbol)
                
                orders = query.limit(limit).all()
                return [
                    {
                        'id': order.id,
                        'symbol': order.symbol,
                        'side': order.side,
                        'order_type': order.order_type,
                        'amount': order.amount,
                        'price': order.price,
                        'status': order.status,
                        'filled_amount': order.filled_amount,
                        'average_price': order.average_price,
                        'fee': order.fee,
                        'created_at': order.created_at,
                        'filled_at': order.filled_at
                    }
                    for order in orders
                ]
                
        except Exception as e:
            log_error("Error getting order history", e)
            return []
