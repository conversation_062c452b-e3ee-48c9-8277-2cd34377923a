# 📈 Guide des Stratégies de Trading INVESTT

Ce document détaille les stratégies de trading disponibles dans INVESTT et comment en créer de nouvelles.

## 🎯 Stratégies Disponibles

### 1. Stratégie RSI (Relative Strength Index)

#### Description
La stratégie RSI est basée sur l'indicateur technique RSI qui mesure la vitesse et l'amplitude des mouvements de prix. Elle identifie les conditions de surachat et de survente pour générer des signaux de trading.

#### Principe de Fonctionnement
- **Achat** : Quand RSI < 30 (survente)
- **Vente** : Quand RSI > 70 (surachat)
- **Sortie** : Take profit à 3%, Stop loss à 2%

#### Paramètres Configurables
```python
RSI_PERIOD = 14          # Période de calcul du RSI
RSI_OVERSOLD = 30.0      # Seuil de survente
RSI_OVERBOUGHT = 70.0    # Seuil de surachat
```

#### Filtres Additionnels
1. **Filtre de Volume** : Évite les signaux sur faible volume
2. **Filtre de Tendance** : Optionnel, utilise les moyennes mobiles
3. **Proximité des Extremums** : Renforce les signaux près des plus hauts/bas

#### Exemple d'Utilisation
```python
from src.strategies import create_rsi_strategy

# Créer la stratégie RSI
rsi_strategy = create_rsi_strategy(
    symbols=['BTC/USDT', 'ETH/USDT'],
    timeframe='5m',
    rsi_period=14,
    oversold=30,
    overbought=70,
    volume_filter=True,
    trend_filter=False
)

# Ajouter au moteur de trading
trading_engine.add_strategy(rsi_strategy)
```

#### Performance Attendue
- **Win Rate** : 55-65%
- **Profit Factor** : 1.2-1.8
- **Meilleur sur** : Marchés en range, volatilité modérée
- **Éviter sur** : Tendances fortes, annonces importantes

## 🔧 Créer une Nouvelle Stratégie

### Architecture de Base

Toutes les stratégies héritent de `BaseStrategy` :

```python
from src.strategies import BaseStrategy, Signal, StrategyConfig

class MaStrategy(BaseStrategy):
    """Stratégie basée sur les moyennes mobiles"""
    
    def initialize(self):
        """Initialiser les paramètres de la stratégie"""
        self.fast_period = self.parameters.get('fast_period', 10)
        self.slow_period = self.parameters.get('slow_period', 20)
    
    def generate_signal(self, symbol: str, data: pd.DataFrame, current_price: float) -> Optional[Signal]:
        """Générer un signal de trading"""
        if len(data) < self.slow_period:
            return None
        
        # Calculer les moyennes mobiles
        fast_ma = data['close'].rolling(self.fast_period).mean()
        slow_ma = data['close'].rolling(self.slow_period).mean()
        
        # Détecter le croisement
        if fast_ma.iloc[-1] > slow_ma.iloc[-1] and fast_ma.iloc[-2] <= slow_ma.iloc[-2]:
            # Signal d'achat
            return Signal(
                symbol=symbol,
                action='buy',
                strength=0.8,
                price=current_price,
                timestamp=datetime.utcnow(),
                strategy_name=self.name,
                metadata={'fast_ma': fast_ma.iloc[-1], 'slow_ma': slow_ma.iloc[-1]}
            )
        
        elif fast_ma.iloc[-1] < slow_ma.iloc[-1] and fast_ma.iloc[-2] >= slow_ma.iloc[-2]:
            # Signal de vente
            return Signal(
                symbol=symbol,
                action='sell',
                strength=0.8,
                price=current_price,
                timestamp=datetime.utcnow(),
                strategy_name=self.name,
                metadata={'fast_ma': fast_ma.iloc[-1], 'slow_ma': slow_ma.iloc[-1]}
            )
        
        return None
    
    def should_exit(self, symbol: str, current_price: float, position_info: Dict) -> bool:
        """Déterminer si une position doit être fermée"""
        entry_price = position_info['entry_price']
        side = position_info['side']
        
        if side == 'long':
            profit_pct = (current_price - entry_price) / entry_price
            return profit_pct >= 0.02 or profit_pct <= -0.01  # 2% profit ou 1% perte
        else:
            profit_pct = (entry_price - current_price) / entry_price
            return profit_pct >= 0.02 or profit_pct <= -0.01
```

### Méthodes Obligatoires

#### `initialize()`
Initialise les paramètres spécifiques à la stratégie.

#### `generate_signal()`
Génère des signaux de trading basés sur l'analyse des données.

**Paramètres :**
- `symbol` : Paire de trading (ex: 'BTC/USDT')
- `data` : DataFrame avec données OHLCV
- `current_price` : Prix actuel

**Retour :**
- `Signal` ou `None`

#### `should_exit()`
Détermine si une position existante doit être fermée.

**Paramètres :**
- `symbol` : Paire de trading
- `current_price` : Prix actuel
- `position_info` : Informations sur la position

**Retour :**
- `bool` : True si la position doit être fermée

### Structure du Signal

```python
@dataclass
class Signal:
    symbol: str              # Paire de trading
    action: str             # 'buy', 'sell', 'hold'
    strength: float         # Force du signal (0-1)
    price: float           # Prix du signal
    timestamp: datetime    # Horodatage
    strategy_name: str     # Nom de la stratégie
    metadata: Dict         # Données additionnelles
```

### Bonnes Pratiques

#### 1. Validation des Données
```python
def generate_signal(self, symbol: str, data: pd.DataFrame, current_price: float):
    # Vérifier la quantité de données
    if len(data) < self.min_data_points:
        return None
    
    # Vérifier les valeurs NaN
    if data['close'].isna().any():
        return None
```

#### 2. Gestion des Erreurs
```python
def generate_signal(self, symbol: str, data: pd.DataFrame, current_price: float):
    try:
        # Logique de la stratégie
        pass
    except Exception as e:
        log_error(f"Erreur dans {self.name} pour {symbol}", e)
        return None
```

#### 3. Métadonnées Utiles
```python
metadata = {
    'indicator_value': rsi_value,
    'signal_reason': 'oversold',
    'confidence': 0.8,
    'market_condition': 'ranging',
    'volume_ratio': current_volume / avg_volume
}
```

#### 4. Force du Signal
```python
# Calculer la force basée sur plusieurs facteurs
strength = base_strength
strength *= volume_multiplier
strength *= trend_multiplier
strength = min(1.0, strength)  # Limiter à 1.0
```

## 📊 Indicateurs Techniques Disponibles

### Utilisation de pandas-ta
```python
import pandas_ta as ta

# RSI
rsi = ta.rsi(data['close'], length=14)

# MACD
macd = ta.macd(data['close'])

# Bollinger Bands
bb = ta.bbands(data['close'], length=20)

# Moving Averages
sma = ta.sma(data['close'], length=20)
ema = ta.ema(data['close'], length=20)

# Stochastic
stoch = ta.stoch(data['high'], data['low'], data['close'])
```

### Calculs Manuels
```python
# Moyenne mobile simple
def sma(prices, period):
    return prices.rolling(window=period).mean()

# Moyenne mobile exponentielle
def ema(prices, period):
    return prices.ewm(span=period).mean()

# Volatilité
def volatility(prices, period):
    returns = prices.pct_change()
    return returns.rolling(window=period).std()
```

## 🧪 Tests de Stratégies

### Test Unitaire
```python
def test_strategy():
    # Créer des données de test
    test_data = create_test_data()
    
    # Créer la stratégie
    strategy = MyStrategy(config)
    
    # Tester la génération de signaux
    signal = strategy.generate_signal('BTC/USDT', test_data, 50000)
    
    assert signal is not None
    assert signal.action in ['buy', 'sell', 'hold']
    assert 0 <= signal.strength <= 1
```

### Backtesting
```python
def backtest_strategy(strategy, historical_data):
    """Backtester une stratégie sur des données historiques"""
    results = []
    
    for i in range(len(historical_data)):
        data_slice = historical_data[:i+1]
        current_price = data_slice['close'].iloc[-1]
        
        signal = strategy.generate_signal('BTC/USDT', data_slice, current_price)
        if signal:
            results.append(signal)
    
    return analyze_results(results)
```

## 📈 Optimisation des Stratégies

### Optimisation des Paramètres
```python
def optimize_parameters(strategy_class, data, param_ranges):
    """Optimiser les paramètres d'une stratégie"""
    best_params = None
    best_performance = -float('inf')
    
    for params in generate_param_combinations(param_ranges):
        strategy = strategy_class(params)
        performance = backtest_strategy(strategy, data)
        
        if performance > best_performance:
            best_performance = performance
            best_params = params
    
    return best_params, best_performance
```

### Walk-Forward Analysis
```python
def walk_forward_analysis(strategy, data, train_period, test_period):
    """Analyse walk-forward pour validation robuste"""
    results = []
    
    for start in range(0, len(data) - train_period - test_period, test_period):
        train_data = data[start:start + train_period]
        test_data = data[start + train_period:start + train_period + test_period]
        
        # Optimiser sur les données d'entraînement
        optimized_strategy = optimize_strategy(strategy, train_data)
        
        # Tester sur les données de test
        performance = backtest_strategy(optimized_strategy, test_data)
        results.append(performance)
    
    return results
```

## 🎯 Stratégies Avancées à Implémenter

### 1. Stratégie Multi-Timeframe
Combine plusieurs timeframes pour des signaux plus robustes.

### 2. Stratégie de Momentum
Suit les tendances fortes avec gestion dynamique des stops.

### 3. Stratégie de Mean Reversion
Exploite les retours à la moyenne sur différents horizons.

### 4. Stratégie ML
Utilise l'apprentissage automatique pour prédire les mouvements.

### 5. Stratégie d'Arbitrage
Exploite les différences de prix entre exchanges.

## 📚 Ressources Supplémentaires

- [Documentation pandas-ta](https://github.com/twopirllc/pandas-ta)
- [Guide des indicateurs techniques](https://www.investopedia.com/technical-analysis-4689657)
- [Backtesting avec Python](https://github.com/kernc/backtesting.py)
- [Analyse quantitative](https://quantlib.org/)

---

**💡 Conseil :** Commencez toujours par des stratégies simples et testez-les rigoureusement avant de passer à des approches plus complexes.
