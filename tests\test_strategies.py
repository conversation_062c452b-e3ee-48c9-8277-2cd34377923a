"""
Tests for trading strategies
"""
import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from src.strategies import create_rsi_strategy, Signal
from src.strategies.rsi_strategy import calculate_rsi


class TestRSIStrategy:
    """Test RSI strategy"""
    
    def setup_method(self):
        """Setup test data"""
        # Create sample OHLCV data
        dates = pd.date_range(start='2023-01-01', periods=100, freq='5T')
        np.random.seed(42)  # For reproducible tests
        
        # Generate realistic price data
        base_price = 100
        price_changes = np.random.normal(0, 0.01, 100)
        prices = [base_price]
        
        for change in price_changes[1:]:
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)
        
        self.test_data = pd.DataFrame({
            'timestamp': dates,
            'open': prices,
            'high': [p * (1 + abs(np.random.normal(0, 0.005))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.005))) for p in prices],
            'close': prices,
            'volume': np.random.uniform(1000, 10000, 100)
        })
        self.test_data.set_index('timestamp', inplace=True)
        
        # Create strategy
        self.strategy = create_rsi_strategy(
            symbols=['BTC/USDT'],
            rsi_period=14,
            oversold=30,
            overbought=70
        )
    
    def test_rsi_calculation(self):
        """Test RSI calculation"""
        rsi = calculate_rsi(self.test_data['close'], period=14)
        
        # RSI should be between 0 and 100
        assert rsi.min() >= 0
        assert rsi.max() <= 100
        
        # Should have NaN values for first few periods
        assert pd.isna(rsi.iloc[:13]).all()
        
        # Should have valid values after warmup period
        assert not pd.isna(rsi.iloc[20:]).any()
    
    def test_strategy_initialization(self):
        """Test strategy initialization"""
        assert self.strategy.name == "RSI_Strategy"
        assert self.strategy.enabled == True
        assert 'BTC/USDT' in self.strategy.symbols
        assert self.strategy.rsi_period == 14
        assert self.strategy.oversold_threshold == 30
        assert self.strategy.overbought_threshold == 70
    
    def test_signal_generation_insufficient_data(self):
        """Test signal generation with insufficient data"""
        # Use only first 10 rows (insufficient for RSI calculation)
        small_data = self.test_data.head(10)
        signal = self.strategy.generate_signal('BTC/USDT', small_data, 100.0)
        
        assert signal is None
    
    def test_signal_generation_oversold(self):
        """Test buy signal generation in oversold condition"""
        # Create oversold scenario
        oversold_data = self.test_data.copy()
        # Force RSI to be oversold by creating declining prices
        declining_prices = np.linspace(110, 90, len(oversold_data))
        oversold_data['close'] = declining_prices
        
        signal = self.strategy.generate_signal('BTC/USDT', oversold_data, 90.0)
        
        if signal:  # Signal might not be generated due to strength threshold
            assert signal.action == 'buy'
            assert signal.symbol == 'BTC/USDT'
            assert signal.strength > 0
            assert signal.strategy_name == "RSI_Strategy"
    
    def test_signal_generation_overbought(self):
        """Test sell signal generation in overbought condition"""
        # Create overbought scenario
        overbought_data = self.test_data.copy()
        # Force RSI to be overbought by creating rising prices
        rising_prices = np.linspace(90, 110, len(overbought_data))
        overbought_data['close'] = rising_prices
        
        signal = self.strategy.generate_signal('BTC/USDT', overbought_data, 110.0)
        
        if signal:  # Signal might not be generated due to strength threshold
            assert signal.action == 'sell'
            assert signal.symbol == 'BTC/USDT'
            assert signal.strength > 0
            assert signal.strategy_name == "RSI_Strategy"
    
    def test_should_exit_long_profit(self):
        """Test exit logic for profitable long position"""
        position_info = {
            'side': 'long',
            'entry_price': 100.0,
            'amount': 1.0,
            'timestamp': datetime.utcnow()
        }
        
        # Test profit scenario (3% gain)
        current_price = 103.0
        should_exit = self.strategy.should_exit('BTC/USDT', current_price, position_info)
        assert should_exit == True
    
    def test_should_exit_long_loss(self):
        """Test exit logic for losing long position"""
        position_info = {
            'side': 'long',
            'entry_price': 100.0,
            'amount': 1.0,
            'timestamp': datetime.utcnow()
        }
        
        # Test loss scenario (2% loss)
        current_price = 98.0
        should_exit = self.strategy.should_exit('BTC/USDT', current_price, position_info)
        assert should_exit == True
    
    def test_should_exit_no_exit(self):
        """Test no exit scenario"""
        position_info = {
            'side': 'long',
            'entry_price': 100.0,
            'amount': 1.0,
            'timestamp': datetime.utcnow()
        }
        
        # Test neutral scenario (1% gain)
        current_price = 101.0
        should_exit = self.strategy.should_exit('BTC/USDT', current_price, position_info)
        assert should_exit == False
    
    def test_performance_tracking(self):
        """Test performance metrics tracking"""
        initial_metrics = self.strategy.get_performance_metrics()
        
        assert initial_metrics['total_signals'] == 0
        assert initial_metrics['successful_signals'] == 0
        assert initial_metrics['failed_signals'] == 0
        assert initial_metrics['total_pnl'] == 0.0
        assert initial_metrics['win_rate'] == 0.0
        
        # Update performance
        self.strategy.update_performance(10.0, True)
        self.strategy.update_performance(-5.0, False)
        
        updated_metrics = self.strategy.get_performance_metrics()
        assert updated_metrics['total_pnl'] == 5.0
        assert updated_metrics['successful_signals'] == 1
        assert updated_metrics['failed_signals'] == 1
    
    def test_strategy_enable_disable(self):
        """Test strategy enable/disable functionality"""
        assert self.strategy.enabled == True
        
        self.strategy.disable()
        assert self.strategy.enabled == False
        
        self.strategy.enable()
        assert self.strategy.enabled == True
    
    def test_parameter_update(self):
        """Test parameter update functionality"""
        new_params = {
            'rsi_period': 21,
            'oversold': 25,
            'overbought': 75
        }
        
        self.strategy.update_parameters(new_params)
        
        assert self.strategy.parameters['rsi_period'] == 21
        assert self.strategy.parameters['oversold'] == 25
        assert self.strategy.parameters['overbought'] == 75
    
    def test_rsi_analysis(self):
        """Test RSI analysis functionality"""
        analysis = self.strategy.get_rsi_analysis('BTC/USDT', self.test_data)
        
        assert 'symbol' in analysis
        assert 'rsi_stats' in analysis
        assert 'condition' in analysis
        assert 'signal_strength' in analysis
        assert 'parameters' in analysis
        
        assert analysis['symbol'] == 'BTC/USDT'
        assert analysis['condition'] in ['oversold', 'overbought', 'bullish', 'bearish', 'neutral']


class TestSignal:
    """Test Signal class"""
    
    def test_signal_creation(self):
        """Test signal creation"""
        signal = Signal(
            symbol='BTC/USDT',
            action='buy',
            strength=0.8,
            price=50000.0,
            timestamp=datetime.utcnow(),
            strategy_name='test_strategy',
            metadata={'test': 'data'}
        )
        
        assert signal.symbol == 'BTC/USDT'
        assert signal.action == 'buy'
        assert signal.strength == 0.8
        assert signal.price == 50000.0
        assert signal.strategy_name == 'test_strategy'
        assert signal.metadata['test'] == 'data'
    
    def test_signal_default_metadata(self):
        """Test signal with default metadata"""
        signal = Signal(
            symbol='ETH/USDT',
            action='sell',
            strength=0.6,
            price=3000.0,
            timestamp=datetime.utcnow(),
            strategy_name='test_strategy'
        )
        
        assert signal.metadata == {}


if __name__ == "__main__":
    pytest.main([__file__])
