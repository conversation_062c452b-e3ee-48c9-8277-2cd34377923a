"""
Installation script for INVESTT Trading Bot
"""
import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Run a command and handle errors"""
    print(f"📦 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_python_version():
    """Check Python version"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ is required")
        return False
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} detected")
    return True


def install_dependencies():
    """Install Python dependencies"""
    print("\n🔧 Installing dependencies...")
    
    # Core dependencies first
    core_deps = [
        "pandas>=2.0.0",
        "numpy>=1.24.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.0.0",
        "loguru>=0.7.0"
    ]
    
    for dep in core_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep.split('>=')[0]}"):
            return False
    
    # Trading dependencies
    trading_deps = [
        "ccxt>=4.0.0",
        "pandas-ta>=0.3.14b0",
        "yfinance>=0.2.0",
        "websocket-client>=1.6.0"
    ]
    
    for dep in trading_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep.split('>=')[0]}"):
            return False
    
    # Database dependencies
    db_deps = [
        "sqlalchemy>=2.0.0",
        "alembic>=1.12.0"
    ]
    
    for dep in db_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep.split('>=')[0]}"):
            return False
    
    # Web and visualization
    web_deps = [
        "streamlit>=1.25.0",
        "plotly>=5.15.0",
        "matplotlib>=3.7.0"
    ]
    
    for dep in web_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep.split('>=')[0]}"):
            return False
    
    # Testing dependencies
    test_deps = [
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0"
    ]
    
    for dep in test_deps:
        if not run_command(f"pip install {dep}", f"Installing {dep.split('>=')[0]}"):
            return False
    
    return True


def setup_directories():
    """Create necessary directories"""
    directories = [
        'logs',
        'data',
        'backups'
    ]
    
    print("\n📁 Creating directories...")
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")


def setup_environment():
    """Setup environment file"""
    env_example = Path('.env.example')
    env_file = Path('.env')
    
    print("\n⚙️ Setting up environment...")
    
    if not env_file.exists() and env_example.exists():
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        print("✅ Created .env file from template")
        print("⚠️  Please edit .env file with your API keys and settings")
    else:
        print("✅ .env file already exists")


def test_installation():
    """Test the installation"""
    print("\n🧪 Testing installation...")
    
    try:
        # Test imports
        import pandas as pd
        import numpy as np
        import ccxt
        import streamlit as st
        print("✅ Core dependencies imported successfully")
        
        # Test database creation
        from src.database import init_db
        init_db()
        print("✅ Database initialized successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Installation test failed: {e}")
        return False


def main():
    """Main installation function"""
    print("🚀 INVESTT Trading Bot Installation")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("\n❌ Dependency installation failed")
        sys.exit(1)
    
    # Setup directories
    setup_directories()
    
    # Setup environment
    setup_environment()
    
    # Test installation
    if not test_installation():
        print("\n❌ Installation test failed")
        sys.exit(1)
    
    print("\n🎉 Installation completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file with your API keys")
    print("2. Run tests: python -m pytest tests/")
    print("3. Start the bot: python main.py")
    print("4. View dashboard: streamlit run dashboard.py")
    
    print("\n⚠️  Important:")
    print("- Start with paper trading (PAPER_TRADING=true)")
    print("- Use testnet for initial tests (BINANCE_TESTNET=true)")
    print("- Never risk more than you can afford to lose")


if __name__ == "__main__":
    main()
