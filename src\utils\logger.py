"""
Advanced logging system for INVESTT Trading Bot
"""
import os
import sys
from pathlib import Path
from loguru import logger
from typing import Optional
from config import config


class TradingLogger:
    """Enhanced logger for trading operations"""
    
    def __init__(self):
        self.setup_logger()
    
    def setup_logger(self):
        """Configure loguru logger with multiple outputs"""
        # Remove default logger
        logger.remove()
        
        # Create logs directory if it doesn't exist
        log_dir = Path(config.LOG_FILE).parent
        log_dir.mkdir(exist_ok=True)
        
        # Console output with colors
        logger.add(
            sys.stdout,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            level=config.LOG_LEVEL,
            colorize=True
        )
        
        # File output for all logs
        logger.add(
            config.LOG_FILE,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            level="DEBUG",
            rotation="1 day",
            retention="30 days",
            compression="zip"
        )
        
        # Separate file for trading operations
        logger.add(
            log_dir / "trading.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {message}",
            level="INFO",
            rotation="1 day",
            retention="90 days",
            filter=lambda record: "TRADE" in record["extra"]
        )
        
        # Error file
        logger.add(
            log_dir / "errors.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
            level="ERROR",
            rotation="1 week",
            retention="12 weeks"
        )
        
        # Performance metrics file
        logger.add(
            log_dir / "performance.log",
            format="{time:YYYY-MM-DD HH:mm:ss} | {message}",
            level="INFO",
            rotation="1 day",
            retention="365 days",
            filter=lambda record: "PERFORMANCE" in record["extra"]
        )
    
    def trade_log(self, message: str, **kwargs):
        """Log trading operations"""
        logger.bind(TRADE=True).info(message, **kwargs)
    
    def performance_log(self, message: str, **kwargs):
        """Log performance metrics"""
        logger.bind(PERFORMANCE=True).info(message, **kwargs)
    
    def strategy_log(self, strategy_name: str, message: str, **kwargs):
        """Log strategy-specific information"""
        logger.bind(STRATEGY=strategy_name).info(f"[{strategy_name}] {message}", **kwargs)
    
    def risk_log(self, message: str, **kwargs):
        """Log risk management events"""
        logger.bind(RISK=True).warning(f"[RISK] {message}", **kwargs)
    
    def order_log(self, order_type: str, symbol: str, message: str, **kwargs):
        """Log order-related events"""
        logger.bind(ORDER=True).info(f"[{order_type}] {symbol}: {message}", **kwargs)
    
    def error_log(self, message: str, exception: Optional[Exception] = None, **kwargs):
        """Log errors with optional exception details"""
        if exception:
            logger.bind(ERROR=True).error(f"{message}: {str(exception)}", **kwargs)
        else:
            logger.bind(ERROR=True).error(message, **kwargs)


# Global logger instance
trading_logger = TradingLogger()

# Convenience functions
def log_trade(message: str, **kwargs):
    """Log trading operations"""
    trading_logger.trade_log(message, **kwargs)

def log_performance(message: str, **kwargs):
    """Log performance metrics"""
    trading_logger.performance_log(message, **kwargs)

def log_strategy(strategy_name: str, message: str, **kwargs):
    """Log strategy information"""
    trading_logger.strategy_log(strategy_name, message, **kwargs)

def log_risk(message: str, **kwargs):
    """Log risk management events"""
    trading_logger.risk_log(message, **kwargs)

def log_order(order_type: str, symbol: str, message: str, **kwargs):
    """Log order events"""
    trading_logger.order_log(order_type, symbol, message, **kwargs)

def log_error(message: str, exception: Optional[Exception] = None, **kwargs):
    """Log errors"""
    trading_logger.error_log(message, exception, **kwargs)

# Export logger for direct use
__all__ = [
    'trading_logger', 'log_trade', 'log_performance', 'log_strategy',
    'log_risk', 'log_order', 'log_error', 'logger'
]
