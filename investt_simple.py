"""
🤖 INVESTT Trading Bot - Version Ultra-Simple
Version garantie sans problèmes de build
"""
import tkinter as tk
from tkinter import messagebox, ttk
import threading
import time
import random
import math
from datetime import datetime
from pathlib import Path

class SimpleConfig:
    """Configuration simple"""
    def __init__(self):
        self.INITIAL_CAPITAL = 1000.0
        self.MAX_DAILY_LOSS = 150.0
        self.PAPER_TRADING = True
        self.LIVE_TRADING = False
        self.TRADING_PAIRS = ["BTC/USDT", "ETH/USDT", "BNB/USDT"]

config = SimpleConfig()

class SimpleAI:
    """IA simplifiée mais efficace"""
    
    def __init__(self):
        self.confidence = 0.7
        self.ml_accuracy = 0.65
        self.sentiment = 0.5
        self.patterns_count = 0
        self.total_trades = 0
        self.successful_trades = 0
        
        # Paramètres adaptatifs
        self.rsi_oversold = 30.0
        self.rsi_overbought = 70.0
        self.ml_weight = 0.5
        self.tech_weight = 0.5
    
    def calculate_rsi(self, prices):
        """RSI simplifié"""
        if len(prices) < 14:
            return 50.0
        
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        avg_gain = sum(gains[-14:]) / 14
        avg_loss = sum(losses[-14:]) / 14
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return max(0, min(100, rsi))
    
    def analyze_market(self, symbol):
        """Analyse de marché simplifiée"""
        # Générer des prix simulés
        base_price = random.uniform(30000, 70000)
        prices = []
        for i in range(50):
            change = random.uniform(-0.02, 0.02)
            if i == 0:
                prices.append(base_price)
            else:
                new_price = prices[-1] * (1 + change)
                prices.append(new_price)
        
        # Calculs techniques
        rsi = self.calculate_rsi(prices)
        
        # Signal technique
        tech_signal = 0.0
        if rsi < self.rsi_oversold:
            tech_signal = 0.3  # Signal d'achat
        elif rsi > self.rsi_overbought:
            tech_signal = -0.3  # Signal de vente
        
        # Prédiction ML simulée
        ml_prediction = random.uniform(-0.05, 0.05)
        
        # Sentiment simulé
        sentiment_factor = math.sin(time.time() / 3600) * 0.2 + 0.5
        sentiment_signal = (sentiment_factor - 0.5) * 2
        
        # Patterns simulés
        patterns = []
        if random.random() < 0.3:
            patterns.append("Tendance Haussière")
        if random.random() < 0.2:
            patterns.append("Doji")
        
        self.patterns_count = len(patterns)
        self.sentiment = sentiment_factor
        
        # Décision finale
        final_signal = (
            tech_signal * self.tech_weight +
            ml_prediction * self.ml_weight +
            sentiment_signal * 0.2
        )
        
        if abs(final_signal) > 0.2 and self.confidence > 0.6:
            action = "BUY" if final_signal > 0 else "SELL"
            return {
                'action': action,
                'strength': min(abs(final_signal), 1.0),
                'confidence': self.confidence,
                'rsi': rsi,
                'patterns': patterns,
                'sentiment_class': self._classify_sentiment(sentiment_factor)
            }
        
        return None
    
    def _classify_sentiment(self, score):
        """Classifier le sentiment"""
        if score < 0.3:
            return "Baissier"
        elif score < 0.7:
            return "Neutre"
        else:
            return "Haussier"
    
    def learn_from_trade(self, profitable):
        """Apprentissage simple"""
        self.total_trades += 1
        if profitable:
            self.successful_trades += 1
        
        # Adapter les paramètres
        win_rate = self.successful_trades / self.total_trades if self.total_trades > 0 else 0.5
        
        if win_rate < 0.4 and self.total_trades > 10:
            self.confidence = min(0.9, self.confidence + 0.05)
        elif win_rate > 0.7 and self.total_trades > 10:
            self.confidence = max(0.5, self.confidence - 0.02)
        
        # Mettre à jour la précision ML
        self.ml_accuracy = win_rate

# Instance globale
ai_engine = SimpleAI()

class SimpleTradingApp:
    """Application de trading simple"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_variables()
        self.create_widgets()
        
        # État du bot
        self.bot_running = False
        self.trades_count = 0
        self.successful_trades = 0
        self.portfolio_value_num = config.INITIAL_CAPITAL
        self.daily_pnl_num = 0.0
    
    def setup_window(self):
        """Configuration de la fenêtre"""
        self.root.title("🤖 INVESTT Trading Bot - Version Simple")
        self.root.geometry("1200x800")
        self.root.configure(bg='#1a1a1a')
        
        # Centrer la fenêtre
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1200 // 2)
        y = (self.root.winfo_screenheight() // 2) - (800 // 2)
        self.root.geometry(f"1200x800+{x}+{y}")
    
    def setup_variables(self):
        """Variables tkinter"""
        self.is_live_trading = tk.BooleanVar(value=False)
        self.portfolio_value = tk.StringVar(value=f"${config.INITIAL_CAPITAL:,.2f}")
        self.daily_pnl = tk.StringVar(value="$0.00")
        self.total_trades = tk.StringVar(value="0")
        self.win_rate = tk.StringVar(value="0%")
        self.status_text = tk.StringVar(value="🔴 Arrêté")
        
        # Métriques IA
        self.ai_confidence = tk.StringVar(value="70%")
        self.ml_accuracy = tk.StringVar(value="65%")
        self.market_sentiment = tk.StringVar(value="Neutre")
        self.patterns_detected = tk.StringVar(value="0")
    
    def create_widgets(self):
        """Créer l'interface"""
        
        # Style
        style = ttk.Style()
        style.theme_use('clam')
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#1a1a1a', foreground='white')
        style.configure('Metric.TLabel', font=('Arial', 12, 'bold'), background='#2d2d2d', foreground='lightblue')
        style.configure('Custom.TFrame', background='#2d2d2d')
        
        # Header
        header_frame = tk.Frame(self.root, bg='#0d1117', height=80)
        header_frame.pack(fill='x', padx=0, pady=0)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(
            header_frame,
            text="🤖 INVESTT TRADING BOT - VERSION SIMPLE",
            font=('Arial', 20, 'bold'),
            bg='#0d1117',
            fg='white'
        )
        title_label.pack(side='left', padx=20, pady=20)
        
        self.status_label = tk.Label(
            header_frame,
            textvariable=self.status_text,
            font=('Arial', 14, 'bold'),
            bg='#0d1117',
            fg='red'
        )
        self.status_label.pack(side='right', padx=20, pady=20)
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#1a1a1a')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Left panel - Controls
        left_frame = tk.Frame(main_frame, bg='#2d2d2d', width=300)
        left_frame.pack(side='left', fill='y', padx=(0, 10))
        left_frame.pack_propagate(False)
        
        self.create_control_panel(left_frame)
        
        # Center panel - Monitoring
        center_frame = tk.Frame(main_frame, bg='#2d2d2d')
        center_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))
        
        self.create_monitoring_panel(center_frame)
        
        # Right panel - AI
        right_frame = tk.Frame(main_frame, bg='#2d2d2d', width=300)
        right_frame.pack(side='right', fill='y')
        right_frame.pack_propagate(False)
        
        self.create_ai_panel(right_frame)
    
    def create_control_panel(self, parent):
        """Panneau de contrôle"""
        
        title = tk.Label(
            parent,
            text="⚙️ CONTRÔLES",
            font=('Arial', 14, 'bold'),
            bg='#2d2d2d',
            fg='white'
        )
        title.pack(pady=(15, 10))
        
        # Mode de trading
        mode_frame = tk.LabelFrame(parent, text="💰 Mode", bg='#2d2d2d', fg='white')
        mode_frame.pack(fill='x', padx=15, pady=10)
        
        self.live_check = tk.Checkbutton(
            mode_frame,
            text="LIVE TRADING",
            variable=self.is_live_trading,
            command=self.toggle_trading_mode,
            bg='#2d2d2d',
            fg='white',
            selectcolor='#2d2d2d',
            font=('Arial', 10, 'bold')
        )
        self.live_check.pack(pady=5)
        
        self.warning_label = tk.Label(
            mode_frame,
            text="⚠️ ARGENT RÉEL !",
            bg='#2d2d2d',
            fg='red',
            font=('Arial', 9, 'bold')
        )
        
        # Boutons principaux
        buttons_frame = tk.LabelFrame(parent, text="🚀 Actions", bg='#2d2d2d', fg='white')
        buttons_frame.pack(fill='x', padx=15, pady=15)
        
        self.start_button = tk.Button(
            buttons_frame,
            text="🚀 DÉMARRER",
            command=self.toggle_bot,
            font=('Arial', 12, 'bold'),
            bg='green',
            fg='white',
            height=2
        )
        self.start_button.pack(fill='x', pady=3)
        
        self.pause_button = tk.Button(
            buttons_frame,
            text="⏸️ PAUSE",
            command=self.pause_bot,
            font=('Arial', 10),
            bg='orange',
            fg='white',
            state='disabled'
        )
        self.pause_button.pack(fill='x', pady=3)
        
        emergency_button = tk.Button(
            buttons_frame,
            text="🛑 ARRÊT D'URGENCE",
            command=self.emergency_stop,
            font=('Arial', 10, 'bold'),
            bg='red',
            fg='white'
        )
        emergency_button.pack(fill='x', pady=3)
        
        # Configuration
        config_frame = tk.LabelFrame(parent, text="⚙️ Configuration", bg='#2d2d2d', fg='white')
        config_frame.pack(fill='x', padx=15, pady=10)
        
        tk.Label(config_frame, text="Capital ($)", bg='#2d2d2d', fg='white').pack(anchor='w', padx=5)
        self.capital_entry = tk.Entry(config_frame)
        self.capital_entry.insert(0, str(config.INITIAL_CAPITAL))
        self.capital_entry.pack(fill='x', padx=5, pady=2)
        
        save_btn = tk.Button(
            config_frame,
            text="💾 Sauvegarder",
            command=self.save_config,
            bg='blue',
            fg='white'
        )
        save_btn.pack(fill='x', padx=5, pady=5)
    
    def create_monitoring_panel(self, parent):
        """Panneau de monitoring"""
        
        title = tk.Label(
            parent,
            text="📊 MONITORING",
            font=('Arial', 14, 'bold'),
            bg='#2d2d2d',
            fg='white'
        )
        title.pack(pady=(15, 10))
        
        # Métriques principales
        metrics_frame = tk.LabelFrame(parent, text="💼 Métriques", bg='#2d2d2d', fg='white')
        metrics_frame.pack(fill='x', padx=15, pady=10)
        
        # Grille 2x2
        grid_frame = tk.Frame(metrics_frame, bg='#2d2d2d')
        grid_frame.pack(fill='x', padx=5, pady=5)
        
        # Portfolio
        portfolio_frame = tk.Frame(grid_frame, bg='#3d3d3d', relief='raised', bd=1)
        portfolio_frame.grid(row=0, column=0, padx=2, pady=2, sticky='ew')
        tk.Label(portfolio_frame, text="💼 Portfolio", bg='#3d3d3d', fg='white', font=('Arial', 9)).pack()
        self.portfolio_label = tk.Label(
            portfolio_frame,
            textvariable=self.portfolio_value,
            bg='#3d3d3d',
            fg='lightblue',
            font=('Arial', 12, 'bold')
        )
        self.portfolio_label.pack()
        
        # P&L
        pnl_frame = tk.Frame(grid_frame, bg='#3d3d3d', relief='raised', bd=1)
        pnl_frame.grid(row=0, column=1, padx=2, pady=2, sticky='ew')
        tk.Label(pnl_frame, text="📈 P&L", bg='#3d3d3d', fg='white', font=('Arial', 9)).pack()
        self.pnl_label = tk.Label(
            pnl_frame,
            textvariable=self.daily_pnl,
            bg='#3d3d3d',
            fg='lightgreen',
            font=('Arial', 12, 'bold')
        )
        self.pnl_label.pack()
        
        # Trades
        trades_frame = tk.Frame(grid_frame, bg='#3d3d3d', relief='raised', bd=1)
        trades_frame.grid(row=1, column=0, padx=2, pady=2, sticky='ew')
        tk.Label(trades_frame, text="🔄 Trades", bg='#3d3d3d', fg='white', font=('Arial', 9)).pack()
        tk.Label(
            trades_frame,
            textvariable=self.total_trades,
            bg='#3d3d3d',
            fg='orange',
            font=('Arial', 12, 'bold')
        ).pack()
        
        # Win Rate
        winrate_frame = tk.Frame(grid_frame, bg='#3d3d3d', relief='raised', bd=1)
        winrate_frame.grid(row=1, column=1, padx=2, pady=2, sticky='ew')
        tk.Label(winrate_frame, text="🎯 Win Rate", bg='#3d3d3d', fg='white', font=('Arial', 9)).pack()
        tk.Label(
            winrate_frame,
            textvariable=self.win_rate,
            bg='#3d3d3d',
            fg='lightgreen',
            font=('Arial', 12, 'bold')
        ).pack()
        
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(1, weight=1)
        
        # Logs
        log_frame = tk.LabelFrame(parent, text="📝 Logs", bg='#2d2d2d', fg='white')
        log_frame.pack(fill='both', expand=True, padx=15, pady=10)
        
        self.log_text = tk.Text(
            log_frame,
            bg='#1a1a1a',
            fg='white',
            font=('Consolas', 9),
            wrap='word'
        )
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        clear_btn = tk.Button(
            log_frame,
            text="🗑️ Effacer",
            command=self.clear_logs,
            bg='gray',
            fg='white'
        )
        clear_btn.pack(pady=5)

    def create_ai_panel(self, parent):
        """Panneau IA"""

        title = tk.Label(
            parent,
            text="🧠 IA SIMPLE",
            font=('Arial', 14, 'bold'),
            bg='#2d2d2d',
            fg='white'
        )
        title.pack(pady=(15, 10))

        # Métriques IA
        ai_frame = tk.LabelFrame(parent, text="🤖 Métriques IA", bg='#2d2d2d', fg='white')
        ai_frame.pack(fill='x', padx=15, pady=10)

        # Confiance
        conf_frame = tk.Frame(ai_frame, bg='#3d3d3d', relief='raised', bd=1)
        conf_frame.pack(fill='x', padx=5, pady=2)
        tk.Label(conf_frame, text="🧠 Confiance", bg='#3d3d3d', fg='white', font=('Arial', 9)).pack()
        tk.Label(
            conf_frame,
            textvariable=self.ai_confidence,
            bg='#3d3d3d',
            fg='cyan',
            font=('Arial', 11, 'bold')
        ).pack()

        # Précision ML
        ml_frame = tk.Frame(ai_frame, bg='#3d3d3d', relief='raised', bd=1)
        ml_frame.pack(fill='x', padx=5, pady=2)
        tk.Label(ml_frame, text="🎯 Précision ML", bg='#3d3d3d', fg='white', font=('Arial', 9)).pack()
        tk.Label(
            ml_frame,
            textvariable=self.ml_accuracy,
            bg='#3d3d3d',
            fg='yellow',
            font=('Arial', 11, 'bold')
        ).pack()

        # Sentiment
        sentiment_frame = tk.LabelFrame(parent, text="📰 Sentiment", bg='#2d2d2d', fg='white')
        sentiment_frame.pack(fill='x', padx=15, pady=10)

        self.sentiment_label = tk.Label(
            sentiment_frame,
            textvariable=self.market_sentiment,
            bg='#2d2d2d',
            fg='lightgreen',
            font=('Arial', 12, 'bold')
        )
        self.sentiment_label.pack(pady=5)

        # Patterns
        patterns_frame = tk.LabelFrame(parent, text="🔍 Patterns", bg='#2d2d2d', fg='white')
        patterns_frame.pack(fill='x', padx=15, pady=10)

        tk.Label(
            patterns_frame,
            textvariable=self.patterns_detected,
            bg='#2d2d2d',
            fg='orange',
            font=('Arial', 12, 'bold')
        ).pack(pady=5)

        # Actions IA
        actions_frame = tk.LabelFrame(parent, text="🔧 Actions IA", bg='#2d2d2d', fg='white')
        actions_frame.pack(fill='x', padx=15, pady=10)

        optimize_btn = tk.Button(
            actions_frame,
            text="⚡ Optimiser",
            command=self.optimize_ai,
            bg='purple',
            fg='white'
        )
        optimize_btn.pack(fill='x', padx=5, pady=2)

        analyze_btn = tk.Button(
            actions_frame,
            text="📊 Analyser",
            command=self.analyze_market,
            bg='blue',
            fg='white'
        )
        analyze_btn.pack(fill='x', padx=5, pady=2)

        reset_btn = tk.Button(
            actions_frame,
            text="🔄 Reset",
            command=self.reset_ai,
            bg='gray',
            fg='white'
        )
        reset_btn.pack(fill='x', padx=5, pady=2)

    def toggle_trading_mode(self):
        """Basculer mode trading"""
        if self.is_live_trading.get():
            result = messagebox.askyesno(
                "⚠️ LIVE TRADING",
                "ATTENTION: Mode LIVE TRADING activé!\n\n"
                "Vous allez trader avec de l'ARGENT RÉEL.\n"
                "Êtes-vous sûr?",
                icon="warning"
            )

            if result:
                config.LIVE_TRADING = True
                config.PAPER_TRADING = False
                self.warning_label.pack(pady=2)
                self.log_message("⚠️ LIVE TRADING ACTIVÉ!")
            else:
                self.is_live_trading.set(False)
        else:
            config.LIVE_TRADING = False
            config.PAPER_TRADING = True
            self.warning_label.pack_forget()
            self.log_message("📝 Paper Trading activé")

    def toggle_bot(self):
        """Démarrer/Arrêter le bot"""
        if not self.bot_running:
            self.start_bot()
        else:
            self.stop_bot()

    def start_bot(self):
        """Démarrer le bot"""
        try:
            self.bot_running = True
            self.start_button.configure(text="🛑 ARRÊTER", bg="red")
            self.pause_button.configure(state="normal")
            self.status_text.set("🤖 IA EN FONCTIONNEMENT")
            self.status_label.configure(fg="green")

            mode = "LIVE" if self.is_live_trading.get() else "PAPER"
            self.log_message(f"🚀 Bot IA démarré en mode {mode}")
            self.log_message("🧠 IA Simple activée:")
            self.log_message("  • Analyse technique (RSI)")
            self.log_message("  • Prédictions ML légères")
            self.log_message("  • Sentiment simulé")
            self.log_message("  • Apprentissage adaptatif")

            # Démarrer la simulation
            self.start_simulation()

        except Exception as e:
            self.log_message(f"❌ Erreur: {e}")
            messagebox.showerror("Erreur", f"Erreur démarrage:\n{e}")

    def stop_bot(self):
        """Arrêter le bot"""
        self.bot_running = False
        self.start_button.configure(text="🚀 DÉMARRER", bg="green")
        self.pause_button.configure(state="disabled")
        self.status_text.set("🔴 IA ARRÊTÉE")
        self.status_label.configure(fg="red")
        self.log_message("🛑 Bot IA arrêté")

    def pause_bot(self):
        """Pause/Reprendre"""
        # Implémentation simple
        self.log_message("⏸️ Fonction pause (à implémenter)")

    def emergency_stop(self):
        """Arrêt d'urgence"""
        result = messagebox.askyesno(
            "🛑 ARRÊT D'URGENCE",
            "Arrêt d'urgence du bot IA?",
            icon="warning"
        )

        if result:
            self.stop_bot()
            self.log_message("🛑 ARRÊT D'URGENCE EFFECTUÉ!")

    def save_config(self):
        """Sauvegarder config"""
        try:
            config.INITIAL_CAPITAL = float(self.capital_entry.get())
            self.log_message("💾 Configuration sauvegardée")
            messagebox.showinfo("Config", "Configuration sauvegardée!")
        except ValueError:
            messagebox.showerror("Erreur", "Valeur invalide!")

    def optimize_ai(self):
        """Optimiser l'IA"""
        self.log_message("⚡ Optimisation IA...")

        def optimize():
            time.sleep(2)
            ai_engine.confidence = min(0.9, ai_engine.confidence + 0.05)
            self.root.after(0, lambda: self.log_message("✅ IA optimisée!"))

        threading.Thread(target=optimize, daemon=True).start()

    def analyze_market(self):
        """Analyser le marché"""
        self.log_message("📊 Analyse de marché...")

        def analyze():
            time.sleep(3)
            result = ai_engine.analyze_market("BTC/USDT")
            if result:
                analysis = f"""🔍 ANALYSE COMPLÈTE:
• Action: {result['action']}
• Force: {result['strength']:.2f}
• Confiance: {result['confidence']:.2f}
• RSI: {result['rsi']:.1f}
• Sentiment: {result['sentiment_class']}
• Patterns: {len(result['patterns'])}"""
            else:
                analysis = "🔍 ANALYSE: Aucun signal fort détecté"

            self.root.after(0, lambda: self.log_message("✅ Analyse terminée!"))
            self.root.after(0, lambda: self.log_message(analysis))

        threading.Thread(target=analyze, daemon=True).start()

    def reset_ai(self):
        """Reset IA"""
        result = messagebox.askyesno("Reset", "Reset de l'IA?")
        if result:
            global ai_engine
            ai_engine = SimpleAI()
            self.trades_count = 0
            self.successful_trades = 0
            self.daily_pnl_num = 0.0
            self.update_display()
            self.log_message("🔄 IA reset!")

    def start_simulation(self):
        """Démarrer la simulation"""
        def simulation():
            while self.bot_running:
                try:
                    # Mise à jour des métriques IA
                    self.root.after(0, self.update_ai_metrics)

                    # Simulation de trade
                    if random.random() < 0.2:  # 20% de chance
                        self.root.after(0, self.simulate_trade)

                    time.sleep(3)
                except:
                    break

        threading.Thread(target=simulation, daemon=True).start()

    def update_ai_metrics(self):
        """Mettre à jour les métriques IA"""
        try:
            self.ai_confidence.set(f"{ai_engine.confidence*100:.1f}%")
            self.ml_accuracy.set(f"{ai_engine.ml_accuracy*100:.1f}%")

            # Sentiment
            sentiment_class = ai_engine._classify_sentiment(ai_engine.sentiment)
            self.market_sentiment.set(sentiment_class)

            # Couleur du sentiment
            if ai_engine.sentiment < 0.3:
                color = "red"
            elif ai_engine.sentiment < 0.7:
                color = "orange"
            else:
                color = "lightgreen"
            self.sentiment_label.configure(fg=color)

            # Patterns
            self.patterns_detected.set(str(ai_engine.patterns_count))

        except Exception as e:
            print(f"Erreur update metrics: {e}")

    def simulate_trade(self):
        """Simuler un trade"""
        try:
            symbol = random.choice(config.TRADING_PAIRS)
            decision = ai_engine.analyze_market(symbol)

            if decision:
                self.trades_count += 1

                # Simuler le résultat
                success_prob = decision['confidence'] * decision['strength']
                is_success = random.random() < success_prob

                if is_success:
                    self.successful_trades += 1
                    pnl = random.uniform(5, 25) * decision['strength']
                else:
                    pnl = -random.uniform(3, 15) * decision['strength']

                self.daily_pnl_num += pnl
                self.portfolio_value_num += pnl

                # Apprentissage
                ai_engine.learn_from_trade(is_success)

                # Mise à jour affichage
                self.update_display()

                # Log
                action = "✅ GAIN" if is_success else "❌ PERTE"
                self.log_message(f"{action} {symbol}: {pnl:+.2f}€ (Conf: {decision['confidence']:.1%})")

        except Exception as e:
            print(f"Erreur simulate trade: {e}")

    def update_display(self):
        """Mettre à jour l'affichage"""
        try:
            self.total_trades.set(str(self.trades_count))
            self.portfolio_value.set(f"${self.portfolio_value_num:,.2f}")

            # P&L avec couleur
            pnl_color = "lightgreen" if self.daily_pnl_num >= 0 else "red"
            self.daily_pnl.set(f"${self.daily_pnl_num:,.2f}")
            self.pnl_label.configure(fg=pnl_color)

            # Win rate
            if self.trades_count > 0:
                win_rate = (self.successful_trades / self.trades_count) * 100
                self.win_rate.set(f"{win_rate:.1f}%")

        except Exception as e:
            print(f"Erreur update display: {e}")

    def log_message(self, message):
        """Ajouter un message au log"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"

            self.log_text.insert('end', log_entry)
            self.log_text.see('end')

            # Limiter les lignes
            lines = self.log_text.get("1.0", "end").split("\n")
            if len(lines) > 100:
                self.log_text.delete("1.0", "20.0")

        except Exception as e:
            print(f"Erreur log: {e}")

    def clear_logs(self):
        """Effacer les logs"""
        try:
            self.log_text.delete("1.0", "end")
            self.log_message("🗑️ Logs effacés")
        except:
            pass

    def on_closing(self):
        """Fermeture"""
        if self.bot_running:
            result = messagebox.askyesno(
                "Fermeture",
                "Le bot est en cours.\nL'arrêter et fermer?"
            )
            if result:
                self.stop_bot()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """Lancer l'application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Messages de démarrage
        self.log_message("🤖 INVESTT Trading Bot Simple démarré")
        self.log_message("🧠 IA Simple initialisée")
        self.log_message("✅ Modules disponibles:")
        self.log_message("  • Analyse technique RSI")
        self.log_message("  • Prédictions ML légères")
        self.log_message("  • Sentiment simulé")
        self.log_message("  • Apprentissage adaptatif")
        self.log_message("💡 Version garantie sans problèmes")
        self.log_message("🚀 Configurez et démarrez!")

        self.root.mainloop()


def main():
    """Point d'entrée"""
    try:
        app = SimpleTradingApp()
        app.run()
    except Exception as e:
        messagebox.showerror("Erreur", f"Erreur critique:\n{e}")


if __name__ == "__main__":
    main()
