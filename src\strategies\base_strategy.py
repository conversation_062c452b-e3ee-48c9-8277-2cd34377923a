"""
Base strategy class for all trading strategies
"""
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import pandas as pd

from src.data import Ticker, OHLCV
from src.utils.logger import log_strategy, log_error


@dataclass
class Signal:
    """Trading signal structure"""
    symbol: str
    action: str  # 'buy', 'sell', 'hold'
    strength: float  # Signal strength 0-1
    price: float
    timestamp: datetime
    strategy_name: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class StrategyConfig:
    """Base configuration for strategies"""
    name: str
    enabled: bool = True
    symbols: List[str] = None
    timeframe: str = "5m"
    parameters: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.symbols is None:
            self.symbols = []
        if self.parameters is None:
            self.parameters = {}


class BaseStrategy(ABC):
    """Abstract base class for all trading strategies"""
    
    def __init__(self, config: StrategyConfig):
        self.config = config
        self.name = config.name
        self.enabled = config.enabled
        self.symbols = config.symbols
        self.timeframe = config.timeframe
        self.parameters = config.parameters
        
        # Strategy state
        self.last_signals = {}  # symbol -> last signal
        self.positions = {}     # symbol -> position info
        self.performance_metrics = {
            'total_signals': 0,
            'successful_signals': 0,
            'failed_signals': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
        
        self.initialize()
    
    @abstractmethod
    def initialize(self):
        """Initialize strategy-specific parameters"""
        pass
    
    @abstractmethod
    def generate_signal(self, symbol: str, data: pd.DataFrame, current_price: float) -> Optional[Signal]:
        """
        Generate trading signal based on market data
        
        Args:
            symbol: Trading symbol
            data: Historical OHLCV data
            current_price: Current market price
            
        Returns:
            Signal object or None if no signal
        """
        pass
    
    @abstractmethod
    def should_exit(self, symbol: str, current_price: float, position_info: Dict) -> bool:
        """
        Determine if current position should be exited
        
        Args:
            symbol: Trading symbol
            current_price: Current market price
            position_info: Current position information
            
        Returns:
            True if position should be exited
        """
        pass
    
    def on_ticker_update(self, ticker: Ticker):
        """Handle real-time ticker updates"""
        if not self.enabled or ticker.symbol not in self.symbols:
            return
        
        try:
            # Check for exit signals on existing positions
            if ticker.symbol in self.positions:
                position_info = self.positions[ticker.symbol]
                if self.should_exit(ticker.symbol, ticker.last, position_info):
                    exit_signal = Signal(
                        symbol=ticker.symbol,
                        action='sell' if position_info['side'] == 'long' else 'buy',
                        strength=1.0,
                        price=ticker.last,
                        timestamp=ticker.timestamp,
                        strategy_name=self.name,
                        metadata={'reason': 'exit_signal', 'position_info': position_info}
                    )
                    self._emit_signal(exit_signal)
                    
        except Exception as e:
            log_error(f"Error in ticker update for strategy {self.name}", e)
    
    def on_data_update(self, symbol: str, data: pd.DataFrame, current_price: float):
        """Handle historical data updates"""
        if not self.enabled or symbol not in self.symbols:
            return
        
        try:
            signal = self.generate_signal(symbol, data, current_price)
            if signal:
                self._emit_signal(signal)
                
        except Exception as e:
            log_error(f"Error generating signal for {symbol} in strategy {self.name}", e)
    
    def _emit_signal(self, signal: Signal):
        """Emit a trading signal"""
        self.last_signals[signal.symbol] = signal
        self.performance_metrics['total_signals'] += 1
        
        log_strategy(
            self.name,
            f"Signal generated: {signal.action.upper()} {signal.symbol} at {signal.price} "
            f"(strength: {signal.strength:.2f})"
        )
        
        # Notify signal handlers (will be implemented in trading engine)
        self._notify_signal_handlers(signal)
    
    def _notify_signal_handlers(self, signal: Signal):
        """Notify registered signal handlers"""
        # This will be implemented by the trading engine
        pass
    
    def update_position(self, symbol: str, side: str, entry_price: float, amount: float):
        """Update position information"""
        self.positions[symbol] = {
            'side': side,
            'entry_price': entry_price,
            'amount': amount,
            'timestamp': datetime.utcnow()
        }
    
    def close_position(self, symbol: str):
        """Close position"""
        if symbol in self.positions:
            del self.positions[symbol]
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get strategy performance metrics"""
        if self.performance_metrics['total_signals'] > 0:
            self.performance_metrics['win_rate'] = (
                self.performance_metrics['successful_signals'] / 
                self.performance_metrics['total_signals']
            )
        
        return self.performance_metrics.copy()
    
    def update_performance(self, pnl: float, success: bool):
        """Update performance metrics"""
        self.performance_metrics['total_pnl'] += pnl
        
        if success:
            self.performance_metrics['successful_signals'] += 1
        else:
            self.performance_metrics['failed_signals'] += 1
    
    def enable(self):
        """Enable the strategy"""
        self.enabled = True
        log_strategy(self.name, "Strategy enabled")
    
    def disable(self):
        """Disable the strategy"""
        self.enabled = False
        log_strategy(self.name, "Strategy disabled")
    
    def update_parameters(self, parameters: Dict[str, Any]):
        """Update strategy parameters"""
        self.parameters.update(parameters)
        log_strategy(self.name, f"Parameters updated: {parameters}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get strategy status"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'symbols': self.symbols,
            'timeframe': self.timeframe,
            'parameters': self.parameters,
            'positions': len(self.positions),
            'performance': self.get_performance_metrics()
        }
    
    def __str__(self):
        return f"{self.name} Strategy (enabled: {self.enabled}, symbols: {len(self.symbols)})"
    
    def __repr__(self):
        return f"<{self.__class__.__name__}(name='{self.name}', enabled={self.enabled})>"
