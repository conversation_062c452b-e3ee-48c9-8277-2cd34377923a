"""
Configuration management for INVESTT Trading Bot
"""
import os
from typing import Dict, Any, Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

load_dotenv()


class TradingConfig(BaseSettings):
    """Main configuration class for the trading bot"""
    
    # API Configuration
    BINANCE_API_KEY: str = Field(default="", env="BINANCE_API_KEY")
    BINANCE_SECRET_KEY: str = Field(default="", env="BINANCE_SECRET_KEY")
    BINANCE_TESTNET: bool = Field(default=True, env="BINANCE_TESTNET")
    
    # Database Configuration
    DATABASE_URL: str = Field(default="sqlite:///investt.db", env="DATABASE_URL")
    
    # Trading Parameters
    INITIAL_CAPITAL: float = Field(default=1000.0, env="INITIAL_CAPITAL")
    MAX_POSITION_SIZE: float = Field(default=0.02, env="MAX_POSITION_SIZE")  # 2% per trade
    MAX_DAILY_LOSS: float = Field(default=150.0, env="MAX_DAILY_LOSS")
    MAX_DRAWDOWN: float = Field(default=0.10, env="MAX_DRAWDOWN")
    
    # Risk Management
    STOP_LOSS_PERCENTAGE: float = Field(default=0.02, env="STOP_LOSS_PERCENTAGE")  # 2%
    TAKE_PROFIT_PERCENTAGE: float = Field(default=0.04, env="TAKE_PROFIT_PERCENTAGE")  # 4%
    
    # Strategy Parameters
    RSI_PERIOD: int = Field(default=14, env="RSI_PERIOD")
    RSI_OVERSOLD: float = Field(default=30.0, env="RSI_OVERSOLD")
    RSI_OVERBOUGHT: float = Field(default=70.0, env="RSI_OVERBOUGHT")
    
    MACD_FAST: int = Field(default=12, env="MACD_FAST")
    MACD_SLOW: int = Field(default=26, env="MACD_SLOW")
    MACD_SIGNAL: int = Field(default=9, env="MACD_SIGNAL")
    
    # Timeframes
    TIMEFRAMES: list = Field(default=["1m", "5m", "15m", "1h"], env="TIMEFRAMES")
    PRIMARY_TIMEFRAME: str = Field(default="5m", env="PRIMARY_TIMEFRAME")
    
    # Symbols to trade
    TRADING_PAIRS: list = Field(default=["BTC/USDT", "ETH/USDT", "BNB/USDT"], env="TRADING_PAIRS")
    
    # Logging
    LOG_LEVEL: str = Field(default="INFO", env="LOG_LEVEL")
    LOG_FILE: str = Field(default="logs/investt.log", env="LOG_FILE")
    
    # Notifications
    TELEGRAM_BOT_TOKEN: str = Field(default="", env="TELEGRAM_BOT_TOKEN")
    TELEGRAM_CHAT_ID: str = Field(default="", env="TELEGRAM_CHAT_ID")
    
    EMAIL_SMTP_SERVER: str = Field(default="", env="EMAIL_SMTP_SERVER")
    EMAIL_PORT: int = Field(default=587, env="EMAIL_PORT")
    EMAIL_USERNAME: str = Field(default="", env="EMAIL_USERNAME")
    EMAIL_PASSWORD: str = Field(default="", env="EMAIL_PASSWORD")
    EMAIL_TO: str = Field(default="", env="EMAIL_TO")
    
    # Trading Mode
    PAPER_TRADING: bool = Field(default=True, env="PAPER_TRADING")
    LIVE_TRADING: bool = Field(default=False, env="LIVE_TRADING")
    
    # Performance
    MAX_CONCURRENT_TRADES: int = Field(default=5, env="MAX_CONCURRENT_TRADES")
    ORDER_TIMEOUT: int = Field(default=30, env="ORDER_TIMEOUT")  # seconds
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Global configuration instance
config = TradingConfig()


def get_trading_config() -> TradingConfig:
    """Get the global trading configuration"""
    return config


def update_config(**kwargs) -> None:
    """Update configuration parameters"""
    global config
    for key, value in kwargs.items():
        if hasattr(config, key):
            setattr(config, key, value)


# Risk management parameters
RISK_PARAMS = {
    'max_daily_loss': config.MAX_DAILY_LOSS,
    'max_position_size': config.MAX_POSITION_SIZE,
    'max_drawdown': config.MAX_DRAWDOWN,
    'correlation_limit': 0.7,
    'volatility_filter': True,
    'max_concurrent_trades': config.MAX_CONCURRENT_TRADES
}

# Strategy configurations
STRATEGY_CONFIGS = {
    'rsi_strategy': {
        'period': config.RSI_PERIOD,
        'oversold': config.RSI_OVERSOLD,
        'overbought': config.RSI_OVERBOUGHT,
        'timeframe': config.PRIMARY_TIMEFRAME
    },
    'macd_strategy': {
        'fast_period': config.MACD_FAST,
        'slow_period': config.MACD_SLOW,
        'signal_period': config.MACD_SIGNAL,
        'timeframe': config.PRIMARY_TIMEFRAME
    }
}
