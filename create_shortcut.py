"""
Script pour créer un raccourci bureau pour INVESTT Trading Bot
"""
import os
import sys
from pathlib import Path
import winshell
from win32com.client import Dispatch

def create_desktop_shortcut():
    """Créer un raccourci sur le bureau"""
    try:
        # Chemin du script de lancement
        script_dir = Path(__file__).parent.absolute()
        launcher_path = script_dir / "launch_investt.bat"
        
        # Chemin du raccourci sur le bureau
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "INVESTT Trading Bot.lnk")
        
        # Créer le raccourci
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = str(launcher_path)
        shortcut.WorkingDirectory = str(script_dir)
        shortcut.Description = "INVESTT Trading Bot - Agent IA de Trading Automatisé"
        
        # Icône (optionnel)
        icon_path = script_dir / "assets" / "icon.ico"
        if icon_path.exists():
            shortcut.IconLocation = str(icon_path)
        
        shortcut.save()
        
        print("✅ Raccourci créé sur le bureau: 'INVESTT Trading Bot'")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du raccourci: {e}")
        print("💡 Vous pouvez créer manuellement un raccourci vers 'launch_investt.bat'")
        return False

def create_start_menu_shortcut():
    """Créer un raccourci dans le menu démarrer"""
    try:
        # Chemin du script de lancement
        script_dir = Path(__file__).parent.absolute()
        launcher_path = script_dir / "launch_investt.bat"
        
        # Dossier dans le menu démarrer
        start_menu = winshell.start_menu()
        investt_folder = os.path.join(start_menu, "INVESTT Trading Bot")
        
        # Créer le dossier s'il n'existe pas
        os.makedirs(investt_folder, exist_ok=True)
        
        # Chemin du raccourci
        shortcut_path = os.path.join(investt_folder, "INVESTT Trading Bot.lnk")
        
        # Créer le raccourci
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = str(launcher_path)
        shortcut.WorkingDirectory = str(script_dir)
        shortcut.Description = "INVESTT Trading Bot - Agent IA de Trading Automatisé"
        
        # Icône (optionnel)
        icon_path = script_dir / "assets" / "icon.ico"
        if icon_path.exists():
            shortcut.IconLocation = str(icon_path)
        
        shortcut.save()
        
        print("✅ Raccourci créé dans le menu démarrer")
        return True
        
    except Exception as e:
        print(f"❌ Erreur lors de la création du raccourci menu démarrer: {e}")
        return False

def main():
    """Fonction principale"""
    print("🚀 Création des raccourcis INVESTT Trading Bot")
    print("=" * 50)
    
    # Vérifier si on est sur Windows
    if sys.platform != "win32":
        print("❌ Ce script fonctionne uniquement sur Windows")
        return
    
    try:
        # Installer les dépendances si nécessaire
        try:
            import winshell
            from win32com.client import Dispatch
        except ImportError:
            print("📦 Installation des dépendances pour les raccourcis...")
            os.system("pip install pywin32 winshell")
            import winshell
            from win32com.client import Dispatch
        
        # Créer les raccourcis
        desktop_success = create_desktop_shortcut()
        start_menu_success = create_start_menu_shortcut()
        
        print("\n" + "=" * 50)
        if desktop_success or start_menu_success:
            print("🎉 Raccourcis créés avec succès!")
            print("\n💡 Vous pouvez maintenant lancer INVESTT Trading Bot:")
            if desktop_success:
                print("   - Double-clic sur l'icône du bureau")
            if start_menu_success:
                print("   - Menu Démarrer > INVESTT Trading Bot")
            print("   - Ou double-clic sur 'launch_investt.bat'")
        else:
            print("❌ Échec de la création des raccourcis")
            print("💡 Vous pouvez lancer manuellement avec 'launch_investt.bat'")
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        print("💡 Lancez manuellement avec 'launch_investt.bat'")

if __name__ == "__main__":
    main()
    input("\nAppuyez sur Entrée pour fermer...")
