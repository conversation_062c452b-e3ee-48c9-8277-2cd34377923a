@echo off
title INVESTT Trading Bot Launcher
color 0A

echo.
echo ========================================
echo    🚀 INVESTT TRADING BOT LAUNCHER
echo ========================================
echo.

:: Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python n'est pas installé ou pas dans le PATH
    echo Veuillez installer Python 3.8+ depuis https://python.org
    pause
    exit /b 1
)

:: Aller dans le répertoire du script
cd /d "%~dp0"

:: Vérifier si les dépendances sont installées
echo 📦 Vérification des dépendances...
python -c "import customtkinter" >nul 2>&1
if errorlevel 1 (
    echo 📥 Installation des dépendances manquantes...
    python -m pip install customtkinter pandas numpy python-dotenv loguru ccxt pandas-ta websocket-client sqlalchemy plotly
    if errorlevel 1 (
        echo ❌ Erreur lors de l'installation des dépendances
        pause
        exit /b 1
    )
)

:: <PERSON><PERSON><PERSON> le fichier .env s'il n'existe pas
if not exist ".env" (
    echo 📝 Création du fichier de configuration...
    copy ".env.example" ".env" >nul 2>&1
    echo ✅ Fichier .env créé - Veuillez le configurer avec vos clés API
)

:: Créer les dossiers nécessaires
if not exist "logs" mkdir logs
if not exist "data" mkdir data
if not exist "backups" mkdir backups

echo ✅ Vérifications terminées
echo.
echo 🚀 Lancement de INVESTT Trading Bot...
echo.

:: Lancer l'application
python investt_app.py

:: Si erreur, afficher le message
if errorlevel 1 (
    echo.
    echo ❌ Erreur lors du lancement de l'application
    echo Vérifiez les logs pour plus d'informations
    pause
)

echo.
echo 👋 Application fermée
pause
