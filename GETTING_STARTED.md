# 🚀 Guide de Démarrage Rapide - INVESTT

Bienvenue dans INVESTT, votre agent IA de trading automatisé ! Ce guide vous accompagne pas à pas pour démarrer en toute sécurité.

## ⚡ Démarrage Ultra-Rapide (5 minutes)

### 1. Installation Automatique
```bash
# Cloner le projet
git clone <repository-url>
cd INVESTT

# Installation automatique
python install.py
```

### 2. Démo Immédiate
```bash
# Lancer la démo (mode sécurisé)
python quick_start.py
```

### 3. Dashboard
```bash
# Dans un nouveau terminal
streamlit run dashboard.py
```

**🎉 Félicitations ! Votre bot de trading IA est opérationnel !**

## 📋 Checklist de Sécurité

Avant de commencer le trading réel, vérifiez ces points :

- [ ] ✅ Bot testé en mode paper trading
- [ ] ✅ Configuration des limites de risque
- [ ] ✅ Clés API configurées avec permissions limitées
- [ ] ✅ Tests sur Binance Testnet
- [ ] ✅ Surveillance active pendant les premières heures
- [ ] ✅ Capital de test limité (max 1-5% de votre portefeuille)

## 🎯 Modes de Fonctionnement

### Mode Paper Trading (Recommandé pour débuter)
```env
PAPER_TRADING=true
LIVE_TRADING=false
```
- ✅ Aucun risque financier
- ✅ Test des stratégies
- ✅ Apprentissage du système

### Mode Testnet (Pour tests avec API)
```env
BINANCE_TESTNET=true
PAPER_TRADING=false
```
- ✅ API réelles mais argent virtuel
- ✅ Test de connectivité
- ✅ Validation des ordres

### Mode Live Trading (Experts uniquement)
```env
BINANCE_TESTNET=false
LIVE_TRADING=true
PAPER_TRADING=false
```
- ⚠️ Argent réel en jeu
- ⚠️ Surveillance obligatoire
- ⚠️ Commencer petit

## ⚙️ Configuration Essentielle

### Fichier .env (Paramètres Principaux)
```env
# API Binance
BINANCE_API_KEY=votre_cle_api
BINANCE_SECRET_KEY=votre_cle_secrete
BINANCE_TESTNET=true

# Capital et Risques
INITIAL_CAPITAL=1000.0
MAX_DAILY_LOSS=50.0          # Perte max par jour
MAX_POSITION_SIZE=0.02       # 2% max par trade
MAX_DRAWDOWN=0.10           # 10% drawdown max

# Stratégie RSI
RSI_PERIOD=14
RSI_OVERSOLD=30.0
RSI_OVERBOUGHT=70.0

# Paires de trading
TRADING_PAIRS=BTC/USDT,ETH/USDT
PRIMARY_TIMEFRAME=5m
```

### Paramètres de Sécurité Recommandés

#### Pour Débutants
```env
INITIAL_CAPITAL=500.0
MAX_DAILY_LOSS=25.0
MAX_POSITION_SIZE=0.01
MAX_CONCURRENT_TRADES=2
```

#### Pour Utilisateurs Expérimentés
```env
INITIAL_CAPITAL=5000.0
MAX_DAILY_LOSS=150.0
MAX_POSITION_SIZE=0.03
MAX_CONCURRENT_TRADES=5
```

## 🎮 Commandes Principales

### Démarrage du Bot
```bash
# Mode normal
python main.py

# Mode debug
python main.py --debug

# Mode paper trading forcé
python main.py --paper
```

### Dashboard et Monitoring
```bash
# Dashboard principal
streamlit run dashboard.py

# Dashboard sur port spécifique
streamlit run dashboard.py --server.port 8502
```

### Tests et Validation
```bash
# Tests complets
python -m pytest tests/ -v

# Test d'une stratégie spécifique
python -m pytest tests/test_strategies.py -v

# Test de connectivité
python -c "from src.data import market_data_manager; print('✅ Connexion OK')"
```

## 📊 Surveillance et Monitoring

### Métriques Clés à Surveiller

1. **P&L Quotidien** : Ne doit pas dépasser MAX_DAILY_LOSS
2. **Drawdown** : Surveiller la baisse depuis le pic
3. **Win Rate** : Pourcentage de trades gagnants
4. **Nombre de Trades** : Activité du bot
5. **Positions Actives** : Exposition actuelle

### Alertes Importantes

- 🔴 **Perte quotidienne > 80% de la limite** : Surveillance renforcée
- 🟡 **Drawdown > 5%** : Vérifier les stratégies
- 🟢 **Win rate < 40%** : Revoir les paramètres
- ⚪ **Aucun trade en 24h** : Vérifier la connectivité

### Logs à Surveiller
```bash
# Logs en temps réel
tail -f logs/investt.log

# Erreurs uniquement
tail -f logs/errors.log

# Trades uniquement
tail -f logs/trading.log
```

## 🛠️ Résolution de Problèmes

### Problèmes Courants

#### "Erreur de connexion API"
```bash
# Vérifier les clés API
python -c "import ccxt; exchange = ccxt.binance({'apiKey': 'votre_cle', 'secret': 'votre_secret', 'sandbox': True}); print(exchange.fetch_balance())"
```

#### "Aucun signal généré"
- Vérifier la volatilité du marché
- Ajuster les paramètres RSI
- Vérifier les filtres de volume

#### "Bot ne démarre pas"
```bash
# Vérifier les dépendances
pip install -r requirements.txt

# Réinitialiser la base de données
python -c "from src.database import reset_db; reset_db()"
```

#### "Dashboard ne s'affiche pas"
```bash
# Réinstaller Streamlit
pip install --upgrade streamlit

# Vérifier le port
netstat -an | grep 8501
```

## 📈 Optimisation des Performances

### Améliorer les Résultats

1. **Ajuster les Paramètres RSI**
   - Marchés volatils : RSI 10-12
   - Marchés calmes : RSI 16-20

2. **Optimiser les Timeframes**
   - Scalping : 1m, 3m
   - Swing : 15m, 1h
   - Position : 4h, 1d

3. **Filtrer les Paires**
   - Commencer avec BTC/USDT, ETH/USDT
   - Ajouter progressivement d'autres paires
   - Éviter les paires trop corrélées

### Backtesting
```python
# Script de backtesting simple
from src.strategies import create_rsi_strategy
from src.data import market_data_manager

# Récupérer des données historiques
data = market_data_manager.get_historical_data('BTC/USDT', '5m', 1000)

# Tester la stratégie
strategy = create_rsi_strategy(['BTC/USDT'])
# ... logique de backtesting
```

## 🔐 Sécurité Avancée

### Configuration API Binance

1. **Créer des clés API dédiées**
   - Permissions : Spot Trading uniquement
   - Pas de Withdrawal
   - Restriction IP si possible

2. **Surveillance des Permissions**
   ```python
   # Vérifier les permissions
   exchange = ccxt.binance({'apiKey': 'key', 'secret': 'secret'})
   account = exchange.fetch_account()
   print(account['permissions'])
   ```

3. **Rotation des Clés**
   - Changer les clés API mensuellement
   - Utiliser des clés différentes pour test/prod

### Sauvegarde des Données
```bash
# Sauvegarde automatique
cp investt.db backups/investt_$(date +%Y%m%d).db

# Script de sauvegarde
python -c "
import shutil
from datetime import datetime
shutil.copy('investt.db', f'backups/investt_{datetime.now().strftime(\"%Y%m%d_%H%M%S\")}.db')
print('✅ Sauvegarde créée')
"
```

## 🎓 Formation et Ressources

### Concepts Essentiels à Maîtriser

1. **Analyse Technique**
   - RSI, MACD, Bollinger Bands
   - Support/Résistance
   - Tendances et retournements

2. **Gestion des Risques**
   - Position sizing
   - Stop loss / Take profit
   - Corrélation des actifs

3. **Psychologie du Trading**
   - Discipline
   - Patience
   - Gestion émotionnelle

### Ressources Recommandées

- 📚 "Technical Analysis of the Financial Markets" - John Murphy
- 🎥 Chaînes YouTube spécialisées trading algorithmique
- 📊 TradingView pour l'analyse graphique
- 💬 Communautés Discord/Telegram de trading

## 🚀 Prochaines Étapes

### Niveau Débutant (Semaines 1-4)
1. Maîtriser le paper trading
2. Comprendre les métriques
3. Ajuster les paramètres de base
4. Surveiller quotidiennement

### Niveau Intermédiaire (Mois 2-3)
1. Tester sur Binance Testnet
2. Optimiser les stratégies
3. Ajouter de nouvelles paires
4. Implémenter des alertes

### Niveau Avancé (Mois 4+)
1. Trading live avec petit capital
2. Développer de nouvelles stratégies
3. Optimisation par machine learning
4. Diversification multi-exchanges

## 📞 Support et Communauté

### En Cas de Problème
1. Consulter les logs d'erreur
2. Vérifier la documentation
3. Tester en mode paper trading
4. Contacter le support

### Contribuer au Projet
- Signaler des bugs
- Proposer des améliorations
- Partager vos stratégies
- Aider d'autres utilisateurs

---

**🎯 Objectif :** Devenir un trader algorithmique profitable et discipliné

**⚠️ Rappel :** Ne jamais risquer plus que ce que vous pouvez vous permettre de perdre

**🚀 Bonne chance dans votre aventure de trading automatisé !**
